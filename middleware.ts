import { NextRequest, NextResponse } from "next/server";

const protectedPaths = ["/dashboard", "/profile-settings", "/gallery", "/slideshow", "/timeline", "/plans"];
// Define admin-only paths
const adminPaths = ["/admin"];

// Function to check if token needs refresh (30 minutes before expiration)
const shouldRefreshToken = (expiryTime: string): boolean => {
  try {
    // Handle different expiry formats
    if (expiryTime.endsWith("d")) {
      // Format like "7d" - convert to milliseconds
      const days = parseInt(expiryTime.slice(0, -1));
      return false; // For day-based expiry, we'll handle differently
    }

    // For JWT standard expiry time in seconds
    const expiryDate = new Date(parseInt(expiryTime) * 1000);
    const thirtyMinutesFromNow = new Date(Date.now() + 30 * 60 * 1000);
    return expiryDate < thirtyMinutesFromNow;
  } catch (error) {
    console.error("Error parsing token expiry time:", error);
    return false;
  }
};

// Function to refresh token
const refreshToken = async (
  token: string
): Promise<{ newToken: string; expiryTime: string } | null> => {
  try {
    const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
    const response = await fetch(`${baseURL}/api/v1/auth/refresh-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        newToken: data.data.accessToken,
        expiryTime: data.data.expiresIn,
      };
    }
    return null;
  } catch (error) {
    console.error("Error refreshing token:", error);
    return null;
  }
};

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl;

  // get authentication token from Cookies
  const tokenCookies = request.cookies.get("token");  
  const expiryTimeCookie = request.cookies.get("expiryTime");
  const roleCookie = request.cookies.get("role");

  const token = tokenCookies?.value;
  const expiryTime = expiryTimeCookie?.value;
  const role = roleCookie?.value;

  // check if the current path is protected
  const isProtected = protectedPaths.some(
    (path) =>
      pathname.pathname.startsWith(path + "/") || pathname.pathname === path
  );

  // Check if the current path is admin-only
  const isAdminPath = adminPaths.some(
    (path) =>
      pathname.pathname === path || pathname.pathname.startsWith(path + "/")
  );

  // redirect to login if tries to access protected path without token
  if (isProtected && !token) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Handle token refresh if needed
  if (token && expiryTime && shouldRefreshToken(expiryTime)) {
    const refreshResult = await refreshToken(token);

    if (refreshResult) {
      // Create response with redirected URL
      const response = NextResponse.redirect(request.url);

      // Set new cookies with refreshed token
      response.cookies.set("token", refreshResult.newToken, {
        path: "/",
        sameSite: "strict",
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
      });

      response.cookies.set("expiryTime", refreshResult.expiryTime, {
        path: "/",
        sameSite: "strict",
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
      });

      return response;
    }

    // If refresh failed and on protected route, redirect to login
    if (isProtected) {
      // Clear cookies and redirect to login
      const response = NextResponse.redirect(new URL("/login", request.url));
      response.cookies.delete("token");
      response.cookies.delete("role");
      response.cookies.delete("expiryTime");
      return response;
    }
  }

  // Redirect to dashboard if trying to access admin path without admin role
  if (isAdminPath && role !== "Admin") {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Redirect logged-in users away from login/signup pages
  if (
    (pathname.pathname === "/login" || pathname.pathname === "/signup") &&
    token
  ) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Allow the request to continue
  return NextResponse.next();
}

// Configure which paths middleware will run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes
     */
    "/login",
    "/((?!_next/static|_next/image|favicon.ico|img|api|_next/data).*)",
  ],
};
