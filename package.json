{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mui/material": "^7.1.0", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@wavesurfer/react": "^1.0.11", "axios": "^1.9.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "15.3.2", "react": "^19.0.0", "react-datetime": "^3.3.1", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "reactjs-social-login": "^2.6.3", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "storage": "link:redux-persist/lib/storage", "styled-components": "^6.1.18", "tailwind-scrollbar": "^4.0.2", "wavesurfer.js": "^7.9.5", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}