import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "@typescript-eslint/no-explicit-any": "off",  // Disable 'no-explicit-any' rule
      "@typescript-eslint/no-unsafe-function-type": "off",  // Disable 'no-unsafe-function-type' rule
      "@typescript-eslint/no-unsafe-assignment": "off",  // Disable 'no-unsafe-assignment' rule
    }
  }
];

export default eslintConfig;
