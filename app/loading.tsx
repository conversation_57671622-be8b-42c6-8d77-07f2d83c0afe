'use client';
import React from 'react';
import { Skeleton, Box, Container } from '@mui/material';

const MainLoading = () => {
  return (
    <Container maxWidth="lg" className="py-8">
      {/* Header skeleton */}
      <Box className="flex justify-between items-center mb-8">
        <Skeleton variant="rectangular" width={150} height={40} />
        <Box className="flex items-center gap-4">
          <Skeleton variant="circular" width={40} height={40} />
          <Box>
            <Skeleton variant="text" width={120} />
            <Skeleton variant="text" width={80} />
          </Box>
        </Box>
      </Box>

      {/* Hero section skeleton */}
      <Box className="mb-8">
        <Skeleton variant="rectangular" height={250} className="rounded-lg mb-4" />
        <Skeleton variant="text" width="60%" height={40} />
        <Skeleton variant="text" width="40%" />
      </Box>

      {/* Content grid skeleton */}
      <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <Box key={index} className="flex flex-col">
            <Skeleton variant="rectangular" height={180} className="rounded-lg mb-3" />
            <Skeleton variant="text" width="80%" />
            <Skeleton variant="text" width="60%" />
          </Box>
        ))}
      </Box>
    </Container>
  );
};

export default MainLoading;
