declare global {
    interface LogoProps {
      href?: string;
      src?: string;
      alt?: string;
      width?: number;
      height?: number;
      tagline?: string;
      className?: string;
    }
  
    interface UserData {
      fullname: string;
      email: string;
      username: string;
      password: string;
      checkbox: number;
    }
  
    interface ErrorData {
      fullname?: string;
      email?: string;
      username?: string;
      password?: string;
      checkbox?: string;
      api?: string;
    }
  
    interface UserDataSignIn {
      email: string;
      password: string;
    }
  
    interface ErrorDataSignIn {
      email?: string;
      password?: string;
      api?: string;
    }
  
    interface ErrorDataUpdateProfile {
      fullname?: string;
      username?: string;
    }
  
    interface ErrorPasswordProfile {
      currPassword?: string;
      newPassword?: string;
      newConfirmPassword?: string;
    }
  
 
  
    interface PersonalInfoError {
      fullname?: string;
      callName?: string;
      day?: string;
      month?: string;
      year?: string;
    }
  
    interface ContactInfoError {
      address?: string;
      phone?: string;
    }
  
    interface RegistorInfo {
      name: string;
      email: string;
    }
    
    
    type Member = {
      _id: string;
      name: string;
      percentage: number;
      type: string;
    };
  
    // login Data
    interface LoginErrors {
      email?: string;
      password?: string;
      api?: string
    }
    
    interface LoginFormData {
      email: string;
      password: string;
    }
  
    interface Plan {
      name: string;
      monthlyPrice: number;
      yearlyPrice: number;
      features: string[];
      ctaText: string;
    }
    
    interface SignupFormData {
      fullname: string;
      email: string;
      password: string;
      confirmPassword: string;
    }
  
    interface SignupErrors {
      fullName?: string;
      email?: string;
      password?: string;
      confirmPassword?: string;
      api?: string;
    }



    
  }
  
  // This export {} is needed to make the file a module
  export {};
  