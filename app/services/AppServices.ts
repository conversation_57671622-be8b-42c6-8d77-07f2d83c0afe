import ApiConfig from "./apiConfig";
import instance from "./interceptor";

class AppService {
  async signUp(payload: any) {
    try {
      const response = await instance.post(`${ApiConfig.signup}`, payload);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async signIn(payload: any) {
    try {
      const response = await instance.post(`${ApiConfig.signin}`, payload);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async loginWithGoogle(payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.loginWithGoogle}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }
  async loginWithFacebook(payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.loginWithFacebook}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async resetPassword(payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.resetPassword}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async updatePassword(payload: any) {
    try {
      const response = await instance.patch(
        `${ApiConfig.updatePassword}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async signOut() {
    try {
      const response = await instance.post(`${ApiConfig.signout}`);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async verifyUserEmail(token: string) {
    try {
      const response = await instance.get(
        `${ApiConfig.verifyEmail}?token=${token}`
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async refreshToken(token: string) {
    try {
      const response = await instance.get(`${ApiConfig.refreshToken}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async resendVerificationEmail(email: string) {
    try {
      const response = await instance.post(
        `${ApiConfig.resendVerificationEmail}`,
        { email }
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async verifyUserResetPasswordToken(token: string) {
    try {
      const response = await instance.get(
        `${ApiConfig.verifyResPassToken}?token=${token}`
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async ForgotPassword(payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.forgotPassword}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async ResetPasswordField(Payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.resetPasswordField}`,
        Payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  // trusted contacts
  async addTrustedContacts(payload: any) {
    try {
      const response = await instance.post(
        `${ApiConfig.trustedContacts}`,
        payload
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async getTrustedContacts() {
    try {
      const response = await instance.get(`${ApiConfig.trustedContacts}`);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async deleteTrustedContact(id: string) {
    try {
      const response = await instance.delete(
        `${ApiConfig.trustedContacts}/${id}`
      );
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  // get UserInfo
  async getUserInfo() {
    try {
      const response = await instance.get(`${ApiConfig.getUserInfo}`);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  // media
  async addMedia(payload: any) {
    try {
      const response = await instance.post(`${ApiConfig.media}`, payload);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async getAllMedia() {
    try {
      const response = await instance.get(`${ApiConfig.media}`);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async updateMedia(id: string, payload: any) {
    try {
      const response = await instance.patch(`${ApiConfig.media}/${id}`, payload);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }

  async deleteMedia(id: string) {
    try {
      const response = await instance.delete(`${ApiConfig.media}/${id}`);
      return response;
    } catch (error: any) {
      return error.response;
    }
  }
}

const appServiceInstance = new AppService();
export default appServiceInstance;
