import axios from 'axios';
import Cookies from "js-cookie";

const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
const instance = axios.create({
  baseURL: baseURL,
  headers: {
    Accept: "*/*",
  },
});



instance.interceptors.request.use(
  async (config) => {
    const token = Cookies.get('token');

    // const token = localStorage.getItem('token');
    // if( token )
      // checkAndRefreshToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  async (response) => {
    // console.log("res time", response)
    // if( token )
   
    return response;
  },
  (error) => {
    
    return error.response;
    // return Promise.reject(error);
  }
);

export default instance;
