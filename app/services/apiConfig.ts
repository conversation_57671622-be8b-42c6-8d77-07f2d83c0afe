const apiURL = process.env.NEXT_PUBLIC_BASE_URL + "/api/v1";

const ApiConfig = {

  // Auth
  loginWithSocial: `${apiURL}/auth/social`,
  signup: `${apiURL}/auth/register`,
  signin: `${apiURL}/auth/login`,
  signout: `${apiURL}/auth/logout`,
  forgotPassword: `${apiURL}/auth/forgot-password`,
  resetPassword: `${apiURL}/auth/reset-password`,
  verifyEmail: `${apiURL}/auth/verify-email`,
  resendVerificationEmail: `${apiURL}/auth/resend-verification`,
  refreshToken: `${apiURL}/auth/refresh-token`,
  verifyResPassToken: `${apiURL}/auth/verify-email`,
  resetPasswordField: `${apiURL}/auth/reset-password`,
  loginWithGoogle: `${apiURL}/auth/google`,
  loginWithFacebook: `${apiURL}/auth/facebook`,
  updatePersonalData: `${apiURL}/user/update-personal-data`,
  updatePassword: `${apiURL}/profile`,


  // get User profile
  getUserInfo: `${apiURL}/profile`,

  // Media
  media: `${apiURL}/file-manager/media`,


  // add trusted contacts
  trustedContacts: `${apiURL}/trusted-contacts`,
  getRelatedPersons: `${apiURL}/related-persons`,

};

export default ApiConfig;
