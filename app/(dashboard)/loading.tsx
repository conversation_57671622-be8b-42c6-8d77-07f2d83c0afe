'use client';

import React from 'react';
import { Skeleton, Box, Container } from '@mui/material';

const DashboardLoading = () => {
  return (
    <Container maxWidth="lg" className="py-6">
      {/* Dashboard header skeleton */}
      <Box className="flex justify-between items-center mb-6">
        <Skeleton variant="rectangular" width={200} height={36} className="rounded" />
        <Box className="flex items-center gap-3">
          <Skeleton variant="rectangular" width={120} height={36} className="rounded-full" />
          <Skeleton variant="rectangular" width={120} height={36} className="rounded-full" />
        </Box>
      </Box>

      {/* Main content skeleton */}
      <Box className="bg-white rounded-lg p-6 shadow-sm">
        <Box className="flex justify-between items-center mb-4">
          <Skeleton variant="rectangular" width={150} height={28} />
          <Skeleton variant="rectangular" width={100} height={36} className="rounded-full" />
        </Box>
        
        <Box className="mb-6">
          <Skeleton variant="rectangular" height={1} className="mb-6" />
          {[...Array(5)].map((_, index) => (
            <Box key={index} className="flex items-center py-3 gap-4">
              <Skeleton variant="circular" width={40} height={40} />
              <Box className="flex-1">
                <Skeleton variant="text" width="40%" />
                <Skeleton variant="text" width="25%" />
              </Box>
              <Skeleton variant="rectangular" width={80} height={32} className="rounded" />
            </Box>
          ))}
        </Box>
        
        {/* Pagination skeleton */}
        <Box className="flex justify-center gap-2 mt-4">
          {[...Array(3)].map((_, index) => (
            <Skeleton key={index} variant="circular" width={36} height={36} />
          ))}
        </Box>
      </Box>
    </Container>
  );
};

export default DashboardLoading;
