'use client';

import { PLANS } from '@/app/services/constant';
import React, { useState } from 'react';
import { FaCheckCircle } from 'react-icons/fa';
 
const PricingPlans: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'month' | 'year'>('month');

  return (
    <section className="items-start px-6 mt-6 xl:mt-0 max-w-5xl">
      <h2 className="text-2xl md:text-4xl font-bold mb-2">Select a Plan That Suits You</h2>
      <p className="mb-6">Start for free or unlock premium features with our paid plans</p>

      {/* Tabs */}
      <div className="flex justify-start mb-8">
        <div
          role="tablist"
          className="bg-white rounded-full px-2 py-2 border  border-[#D0D0D0] flex space-x-1"
        >
          {(['month', 'year'] as const).map((tab) => (
            <button
              key={tab}
              role="tab"
              aria-selected={activeTab === tab}
              className={`px-4 py-1 h-8 rounded-full text-sm cursor-pointer font-semibold ${
                activeTab === tab ? 'bg-custom-primary text-white' : 'text-custom-primary hover:bg-gray-100'
              }`}
              onClick={() => setActiveTab(tab)}
            >
              {tab === 'month' ? 'Monthly' : 'Yearly (Save 15%)'}
            </button>
          ))}
        </div>
      </div>

      {/* Plans */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-[820px] mb-10">
        {PLANS.map((plan) => (
          <div
            key={plan.name}
            className="bg-custom-secondary/90 flex flex-col p-6 rounded-lg shadow-md"
          >
            <h3 className="text-base md:text-2xl font-semibold mb-1">{plan.name} Plan</h3>
            <p className="text-4xl font-bold text-custom-primary">
              ${activeTab === 'month' ? plan.monthlyPrice : plan.yearlyPrice}
              <span className="text-lg font-normal text-black">
                {activeTab === 'month' ? '/month' : '/year'}
              </span>
            </p>
            <hr className="w-full my-5 border-dashed border-[#857979]" />
            <h4 className="text-lg font-semibold mb-4">Features</h4>
            <ul className="space-y-3 text-sm mb-4">
              {plan.features.map((feature) => (
                <li key={feature} className="flex gap-2">
                  <FaCheckCircle color='#425589'/>
                  {feature}
                </li>
              ))}
            </ul>
            <div className="mt-auto">
              <button className="bg-custom-primary text-white py-3 px-5 rounded-full text-sm font-semibold hover:bg-custom-primary/90 cursor-pointer transition">
                {plan.ctaText}
              </button>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default PricingPlans;