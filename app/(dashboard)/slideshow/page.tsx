"use client";

import { Circle<PERSON><PERSON>, View } from "lucide-react";
import React, { useCallback, useState } from "react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import Image from "next/image";

const pexelsImages = [
  { image: "https://images.pexels.com/photos/414171/pexels-photo-414171.jpeg" },
  { image: "https://images.pexels.com/photos/210186/pexels-photo-210186.jpeg" },
  { image: "https://images.pexels.com/photos/34950/pexels-photo.jpg" },
  { image: "https://images.pexels.com/photos/417173/pexels-photo-417173.jpeg" },
  { image: "https://images.pexels.com/photos/355465/pexels-photo-355465.jpeg" },
  { image: "https://picsum.photos/id/237/800/600" },
  { image: "https://picsum.photos/id/1025/800/600" },
  { image: "https://picsum.photos/id/1059/800/600" },
  { image: "https://picsum.photos/id/1062/800/600" },
  { image: "https://picsum.photos/id/1041/800/600" },
];

const Page = () => {
  const [images, setImages] = useState(pexelsImages);

  const reorder = (list: any[], startIndex: number, endIndex: number) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  const onDragStart = useCallback((start: any) => {
    console.log("Drag started:", start);
  }, []);

  const onDragUpdate = useCallback((update: any) => {
    console.log("Drag updated:", update);
  }, []);

  const onDragEnd = useCallback((result: any) => {
    const { source, destination } = result;

    // If dropped outside a valid location
    if (!destination) {
      console.log("Drop cancelled or outside list.");
      return;
    }

    // If dropped in the same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      console.log("Dropped in same place, no change.");
      return;
    }

    const reordered = reorder(images, source.index, destination.index);
    setImages(reordered);
    console.log("New order:", reordered);
  }, [images]);

  return (
    <div className="p-4">
      <div className="flex justify-end w-full mb-4">
        <button className="flex items-center gap-2 bg-custom-primary text-white px-4 py-2 rounded-3xl">
          <CirclePlus size={20} />
          Create Your Slideshow
        </button>
      </div>

      <DragDropContext
        onDragStart={onDragStart}
        onDragUpdate={onDragUpdate}
        onDragEnd={onDragEnd}
      >
        <Droppable
          droppableId="horizontal-droppable"
          direction="horizontal"
          type="slideShow"
        >
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="flex gap-4 overflow-x-auto bg-gray-200 p-4 rounded-lg"
              style={{
                scrollbarWidth: "auto",
                msScrollbarArrowColor: "black",
                minHeight: "170px",
                border: snapshot.isDraggingOver
                  ? "2px dashed #007bff"
                  : "2px dashed transparent",
              }}
            >
              {images.map((item, index) => (
                <Draggable key={item.image} draggableId={item.image} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className={`shrink-0`}
                      style={{
                        ...provided.draggableProps.style,
                        backgroundColor: snapshot.isDragging ? "#d1fae5" : "#fff",

                        scale: snapshot.isDragging ? "1" : "1",
                        borderRadius: "10px",
                        boxShadow: snapshot.isDragging
                          ? "0 2px 8px rgba(0, 0, 0, 0.2)"
                          : "none",
                      }}
                    >
                      <div className="absolute top-2 right-2 bg-white  px-2 rounded-full cursor-move">
                        {index+1}
                      </div>
                      <Image
                        src={item.image}
                        alt=""
                        width={240}
                        height={240}
                        className="w-60 h-60 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </Draggable>
              ))}

              {provided.placeholder}

            </div>
          )}
        </Droppable>
      </DragDropContext>

      <div className="flex justify-end w-full mt-4">
        <button className="flex items-center gap-2 bg-custom-primary text-white px-4 py-2 rounded-3xl">
          View Slide Show
          <View size={20} />
        </button>
      </div>
    </div>
  );
};

export default Page;