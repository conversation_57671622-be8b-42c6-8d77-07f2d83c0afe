'use client';

import { usePathname, useRouter } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { Skeleton } from "@mui/material";
import Cookies from "js-cookie";
import Image from "next/image";

import Sidebar from "../components/shared/Sidebar";
import DashboardLoading from "./loading";
import UserGetUserInfo from "../hooks/UserGetUserInfo";
import { useAppSelector } from "../store/hooks";
import { getUserInfoData } from "../store/reducers/life.selector";
import SidebarV1 from "../components/shared/SidebarV1";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const title = pathname.split("/")[1];
  const newTitle = title[0].toUpperCase() + title.slice(1);

  const userInfo: any = useAppSelector(getUserInfoData);
  const { data, loading, error }: any = UserGetUserInfo();

  useEffect(() => {
    const token = Cookies.get('token');
    if (!token) {
      router.push('/login');
      return;
    }

    if (!loading) {
      setIsMounted(true);

      if (userInfo?.role?.name === 'Admin') {
        router.push('/admin/dashboard');
      }

      if (error || (!data && !userInfo)) {
        console.log("No user data found after loading, redirecting to login");

        Object.keys(Cookies.get()).forEach(cookieName => {
          Cookies.remove(cookieName);
        });
        localStorage.clear();
        router.push('/login');
      }
    }
  }, [loading, data, error, userInfo, router]);


useEffect(() => {
    if(data) {
        setIsMounted(true);
    }
},[data])

  return (
    <div className="w-full grid xl:grid-cols-[359px_1fr] grid-cols-1">
      {/* <Sidebar /> */}
      <SidebarV1  />
      {/* Main Content */}
      <div className="bg-[#fff] min-h-screen w-full overflow-x-hidden">
        <div className="hidden xl:block mb-7 border-b border-[#999999]">
          <div className="px-6 py-3 flex justify-between items-center">
            <h1 className="text-[18px] md:text-[24px] font-bold">{newTitle}</h1>
            <div className="flex items-center gap-3">
              <div className="w-[45px] h-[45px] rounded-full bg-gray-300">
                <Image
                  src={'/img/profile.jpg'}
                  alt="profile"
                  width={100}
                  height={100}
                  className="max-w-full h-full w-full object-cover rounded-full"
                />
              </div>
              <div className="text-right">
                {!isMounted ? (
                  <div className="space-y-2 w-full">
                    <Skeleton variant="rectangular" width={120} height={16} className="rounded" />
                    <Skeleton variant="rectangular" width={80} height={14} className="rounded" />
                  </div>
                ) : data ? (
                  <>
                    <div className="text-[16px] font-semibold">{data?.name?.toUpperCase() ?? 'User'}</div>
                    <div className="text-sm w-full text-left">{data?.role?.name?.split('_').join(" ") ?? 'Customer'}</div>
                  </>
                ) : (
                  <>
                    <p className="text-[16px] font-semibold">Guest User</p>
                    <p className="text-sm w-full text-left">Not logged in</p>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        <div>
          <Suspense fallback={<DashboardLoading />}>
            {children}
          </Suspense>
        </div>
      </div>
    </div>
  );
}
