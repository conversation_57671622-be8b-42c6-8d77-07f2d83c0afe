'use client';

import InputWithLabel from '@/app/components/ui/InputWithLabel';
import { Plus } from 'lucide-react';
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'; // Import the styles
import React, { useRef, useState } from 'react';
import moment from 'moment';
import { toast } from 'sonner';

const Page = () => {
  const [timelineData, setTimelineData] = useState([]);
  const [selectedDate, setSelectedDate] = useState<moment.Moment | null>(null);
  const [formData, setFormData] = useState({
    heading: '',
    title: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  // Create refs with proper typing
  const headingRef = useRef<HTMLInputElement>(null);
  const titleRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);

  // Handle date change - only store the year
  const handleDateChange = (value: moment.Moment | string) => {
    if (moment.isMoment(value)) {
      // Set to January 1st of the selected year to standardize
      const yearOnly = moment().year(value.year()).startOf('year');
      setSelectedDate(yearOnly);
      console.log("Selected year:", yearOnly.format('YYYY'));
    } else {
      setSelectedDate(null);
    }
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.heading.trim()) {
      toast.error("Heading is required");
      return;
    }

    if (!formData.title.trim()) {
      toast.error("Title is required");
      return;
    }

    if (!selectedDate) {
      toast.error("Please select a year");
      return;
    }

    setLoading(true);

    // Create new timeline item with year only
    const newItem = {
      heading: formData.heading,
      title: formData.title,
      description: formData.description,
      year: selectedDate.year(), // Store just the year as a number
      datetime: selectedDate.toISOString() // Keep ISO string for compatibility
    };

    // Add to timeline data
    setTimelineData(prev => [...prev, newItem]);

    // Reset form
    setFormData({
      heading: '',
      title: '',
      description: ''
    });
    setSelectedDate(null);

    toast.success("Timeline item added successfully");
    setLoading(false);
  };

  // Sort timeline items by year
  const sortedTimelineData = [...timelineData].sort((a: any, b: any) => a.year - b.year);

  return (
    <div>
      <div className='flex justify-center items-center'>
        <div className='text-center shadow-sm w-fit rounded-2xl p-8 border border-gray-300'>
          <div className='text-3xl font-bold mb-4'>
            Set timeline
          </div>
          <form className='flex flex-col gap-4' onSubmit={handleSubmit}>
            <InputWithLabel
              id='heading'
              className='border-0 outline-0 text-2xl border-b rounded-xs font-semibold'
              placeholder='Heading'
              type='text'
              ref={headingRef}
              nextRef={titleRef}
              value={formData.heading}
              onChange={handleInputChange}
            />
            <InputWithLabel
              id='title'
              placeholder='Title'
              className='border-0 outline-0 border-b rounded-xs text-[20px] font-base'
              type='text'
              ref={titleRef}
              nextRef={descriptionRef}
              value={formData.title}
              onChange={handleInputChange}
            />
            <InputWithLabel
              id='description'
              placeholder='Description'
              className='border-0 outline-0 border-b rounded-xs text-[16px] font-sm'
              type='text'
              ref={descriptionRef}
              value={formData.description}
              onChange={handleInputChange}
            />

            <div className="relative">
              <label className="font-semibold block text-left mb-1">Select Year</label>
              <Datetime
                value={selectedDate ? selectedDate : undefined}
                onChange={handleDateChange}
                className="custom-datetime-input"
                timeFormat={false}
                dateFormat="YYYY"
                viewMode="years"
                closeOnSelect={true}
                inputProps={{
                  placeholder: "--Select a year--",
                  className: "w-full border border-gray-300 border-b py-2 px-4 rounded-md"
                }}
              />
            </div>

            <div className="mt-4">
              <button
                type="submit"
                className='bg-custom-primary text-white px-3 flex py-2 rounded-3xl w-full justify-center items-center gap-2'
                disabled={loading}
              >
                {loading ? 'Adding...' : <><Plus /> Add</>}
              </button>
            </div>
          </form>
        </div>
      </div>



      {/* display */}
      {sortedTimelineData.length > 0 && (
        <div className="mt-8 w-full mx-auto overflow-x-scroll">
          {/* <h2 className="text-2xl font-bold mb-4">Timeline Items</h2> */}
          <div className="space-y-4 flex">
            {sortedTimelineData.map((item: any, index) => (
              <div key={index} className="border-gray-300 rounded-lg p-4 w-[400px] flex flex-col gap-6">
                <span className='text-white px-5 py-2 bg-custom-primary rounded-lg w-fit'>{item.year}</span>
                <div className='flex justify-center items-center w-[120%]'>
                  <div className='h-6 w-6 border-5 bg-[#E99B26] rounded-full'>
                  </div>
                  <div className='w-full h-2 bg-black'>
                  </div>
                </div>
                <div className='text-custom-primary capitalize font-semibold'>{item?.heading}</div>
                <div>
                  <div className='text-black capitalize font-base'>{item?.title}</div>
                  <div className='text-gray-600 text-sm'>{item?.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default Page
