'use client';

import React, { useEffect, useState } from 'react';
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';
import { z } from 'zod';
import Button from '@/app/components/ui/Button';
import InputWithLabel from '@/app/components/ui/InputWithLabel';
import { toast } from 'sonner';
import appServiceInstance from '@/app/services/AppServices';
import { Dialog } from '@mui/material';
import { CirclePlus, X } from 'lucide-react';
import Card2 from '@/app/components/ui/Card2';
import AlertDialog from '@/app/components/popups/AlertDialog';

// Schema with datetime as string
const contactsSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email'),
  datetime: z.string().min(1, 'Date and Time is required'),
});

type ContactsType = z.infer<typeof contactsSchema>;

const Page = () => {
  const [open, setOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [contacts, setContacts] = useState<ContactsType>({
    name: '',
    email: '',
    datetime: '',
  });
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    datetime: '',
    api: '',
  });
  const [loading, setLoading] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const [trustedContacts, setTrustedContacts] = useState([]);
  const [selectedContactId, setSelectedContactId] = useState('');

  const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    try {
      contactsSchema.shape[id as keyof ContactsType].parse(value);
      setErrors((prev) => ({
        ...prev,
        [id]: '',
      }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors((prev) => ({
          ...prev,
          [id]: error.errors[0].message,
        }));
      }
    }
  };

  const handleOpen = () => {
    setOpen(true);
  }
  const handleClose = () => {
    setOpen(false);
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setContacts((prev) => ({
      ...prev,
      [id]: value,
    }));
    // Clear error when user types
    if (errors[id as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [id]: '',
      }));
    }
  };

  const handleDateChange = (val: moment.Moment | string) => {
    if (typeof val !== 'string' && val.toISOString) {
      setContacts((prev) => ({
        ...prev,
        datetime: val.toISOString(),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const result = contactsSchema.safeParse(contacts);
    if (!result.success) {
      console.log('Validation errors:', result.error.format());
    } else {
      setLoading(true);
      const payload = {
        name: contacts.name,
        email: contacts.email,
        // scheduledDateTime: contacts.datetime,
      };

      try {
        const response = await appServiceInstance.addTrustedContacts(payload);
        if (response?.status === 201) {
          toast.success("Person added successfully!");
          setOpen(false);
        } else {
          toast.error(response?.data?.message || "Failed to add person");
        }
      } catch (error: any) {
        toast.error( error?.message || "Failed to add person");
      } finally {
        setLoading(false);
        handleClose();
        setContacts({
          name: '',
          email: '',
          datetime: '',
        });
      }
    }
  };

  const getTrustedContacts = async () => {
    try {
      const response = await appServiceInstance.getTrustedContacts();

      if (response?.status === 200) {
        console.log("Trusted contacts response:", response.data.data);
        const contacts = response?.data?.data?.trustedContacts || [];
        
        // Validate each contact has an ID before setting state
        const validContacts = contacts.filter(contact => contact && (contact._id || contact.id));
        if (validContacts.length !== contacts.length) {
          console.warn("Some contacts are missing IDs:", contacts);
        }
        
        setTrustedContacts(validContacts);
      } else {
        toast.error(response?.data?.message || "Failed to fetch trusted contacts");
      }
    } catch (error: any) {
      console.error("Error fetching trusted contacts:", error);
    } finally {
      setLoading(false);
    }
  }


  // Alert Dialog for Delete

  const handleDeleteOpen =  () => {
    setDeleteOpen(true);
  }

  const handleDeleteClose = () => {
    setDeleteOpen(false)
  }

  const hanldeDeleteConfirm = async () => {
    setLoading(true);
    console.log("Attempting to delete contact with ID:", selectedContactId);
    
    if (!selectedContactId) {
        console.error("No contact ID selected for deletion");
        setLoading(false);
        return;
    }
  
    try {
        const response = await appServiceInstance.deleteTrustedContact(selectedContactId);
        if (response?.status === 200) {
            toast.success("Trusted Contact deleted successfully!");
            setDeleteOpen(false);
            // Reset the selected ID after successful deletion
            setSelectedContactId('');
            // Refresh the contacts list
            getTrustedContacts();
        } else {
            toast.error(response?.data?.message || "Failed to delete trusted contact");
        }
    } catch (error: any) {
        console.error("Error deleting contact:", error);
        toast.error("An error occurred while deleting the contact");
    } finally {
        setLoading(false);
    }
  }

  useEffect(() => {
    if (loading) {
      setIsMounted(true);
    }
    getTrustedContacts();
  }, [loading])

  return (

    <div className="flex flex-col justify-center w-full mt-10 ">
      <div className='flex items-center w-full px-10 justify-between  '>
        <div className="text-3xl font-bold text-center">Add Trusted Contacts</div>

        <button className='flex gap-3 cursor-pointer bg-custom-primary text-white px-3 py-2 rounded-3xl w-fit !m-0' onClick={() => { handleOpen() }}>
          Add
          <CirclePlus size={25}  />
        </button>
      </div>

      <div className='flex w-full px-5 pt-10'>
        {isMounted && trustedContacts?.length === 0 && (
          <div className='w-full flex justify-center'>
            <p className='text-2xl font-bold text-center'>No Trusted Contacts</p>
          </div>
        )}

        {isMounted && trustedContacts.map((contact: any, index: number) => (
          <div key={index}>
            <Card2 
              item={contact} 
              handleDeleteOpen={handleDeleteOpen} 
              setSelectedContactId={setSelectedContactId} 
            />
          </div>
        ))}

      </div>

      <Dialog
        open={open}
        onClose={() => { handleClose() }}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="sm"
        fullWidth
        // 1: Let the Paper overflow
        PaperProps={{
          sx: {
            overflow: 'visible',     // <-- allow children to overflow
            p: 0,
            m: 0,
            borderRadius: '20px',
          }
        }}

        // 2: Let the Dialog container overflow
        sx={{
          '& .MuiDialog-container': {
            overflow: 'visible',     // <-- allow the popup to escape
          }
        }}
      >
        <div className='absolute top-4 right-4  p-2 rounded-full hover:bg-custom-secondary cursor-pointer' onClick={() => { handleClose() }} ><X /></div>

        <form
          onSubmit={handleSubmit}
          className="flex flex-col gap-4 rounded-3xl p-10"
        >
          <div className="text-3xl font-bold text-center">Add Trusted Contact</div>

          <InputWithLabel
            id="name"
            label="Name"
            placeholder="John Doe"
            type="text"
            onChange={handleInputChange}
            onBlur={handleBlur}
            value={contacts.name}
            error={errors?.name}
          />

          <InputWithLabel
            id="email"
            label="Email"
            placeholder="<EMAIL>"
            type="email"
            onChange={handleInputChange}
            onBlur={handleBlur}
            value={contacts.email}
            error={errors?.email}
          />

          <label className="font-semibold">Select Date & Time</label>
          <div className='relative'>
            <Datetime
              value={contacts.datetime ? new Date(contacts.datetime) : undefined}
              onChange={handleDateChange}
              className="custom-datetime-input"
              inputProps={{
                placeholder: "--Select a date and time.--",
                className: "w-full border border-gray-300 rounded-3xl py-2 px-4"
              }}
            />
          </div>

          <Button type="submit" isLoading={loading} >Add Person</Button>
        </form>
      </Dialog>


      {/* delete Dialog */}
      <AlertDialog open={deleteOpen} handleClose={handleDeleteClose} handleConfirm={hanldeDeleteConfirm} confirmText="Delete" cancelText="Cancel" title="Delete Trusted Contact" description="Are you sure you want to delete this trusted contact?" />

    </div>
  );
};

export default Page;
