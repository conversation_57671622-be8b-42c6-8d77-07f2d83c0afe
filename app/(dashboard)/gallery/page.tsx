"use client";

import AlertDialog from "@/app/components/popups/AlertDialog";
import SelectContentDialog from "@/app/components/popups/SelectContentDialog";
import SpinnerCustom from "@/app/components/shared/SpinnerCustom";
import Waveform from "@/app/components/shared/WaveFormPlayer";
import WaveformPlayer from "@/app/components/shared/WaveFormPlayer";
import Button from "@/app/components/ui/Button";
import InputWithLabel from "@/app/components/ui/InputWithLabel";
import appServiceInstance from "@/app/services/AppServices";
import { tabs } from "@/app/services/constant";
import { CirclePlus, Trash2, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { z } from "zod";

const CustomTabs = ({
  activeTab,
  setActiveTab,
}: {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}) => {
  return (
    <div className="flex justify-start items-center w-full">
      <div
        role="tablist"
        className="bg-white rounded-full px-2 py-2 border border-[#D0D0D0] flex justify-between space-x-1 w-10/12"
      >
        {tabs.map((tab) => (
          <button
            key={tab}
            role="tab"
            aria-selected={activeTab === tab}
            className={`px-6 py-2 h-10 rounded-full w-full text-sm cursor-pointer font-semibold ${activeTab === tab
              ? "bg-custom-primary text-white"
              : "text-custom-primary hover:bg-gray-100"
              }`}
            onClick={() => setActiveTab(tab)}
          >
            {tab}
          </button>
        ))}
      </div>
    </div>
  );
};

const contentDataSchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
  file: z.any().optional(),
  contentType: z.string().min(1, "Content type is required"),
  textContent: z.string().optional(),
});

type ContentDataType = z.infer<typeof contentDataSchema>;

const Page = () => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState("image");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // dialog
  const [isSelectContentDialogOpen, setIsSelectContentDialogOpen] =
    useState(false);
  const [openSelectedMedia, setOpenSelectedMedia] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [updateData, setUpdateData] = useState({
    title: "",
    description: "",
    alternativeText: "",
    textContent: "",
  });
  const [selectedMediaId, setSelectedMediaId] = useState("");

  // Add this to your state declarations
  const [updateLoading, setUpdateLoading] = useState(false);

  // data
  const [allLoading, setAllLoading] = useState({
    uploadLoading: false,
    fetchLoading: false,
    deleteLoading: false,
  });

  const [selectedContentType, setSelectedContentType] = useState<
    "image" | "video" | "text" | "audio" | ""
  >("image");

  const [allMedia, setAllMedia] = useState<any[]>([]);

  const [contentData, setContentData] = useState<ContentDataType>({
    title: "",
    description: "",
    file: null,
    contentType: "image",
    textContent: "",
  });

  useEffect(() => {
    console.log("updateData state changed:", updateData);
  }, [updateData]);

  // add media
  const handleAddMedia = async () => {
    try {
      setAllLoading((prev) => ({ ...prev, uploadLoading: true }));
      contentDataSchema.parse(contentData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path[0];
          if (path) {
            newErrors[path] = err.message;
          }
        });
        console.log(newErrors);
        toast.error("Please check the form for errors");
        setAllLoading((prev) => ({ ...prev, uploadLoading: false }));
        return;
      }
    }

    const formData = new FormData();
    formData.append("title", contentData?.title ?? "");
    formData.append("description", contentData.description ?? "");
    if (selectedContentType !== "text") {
      formData.append("file", contentData?.file);
    }
    // formData.append("file", contentData?.file ?? "");
    formData.append("textContent", contentData.textContent ?? "");

    try {
      const response = await appServiceInstance.addMedia(formData);
      if (response?.status === 201) {
        toast.success("Content added successfully!");
        setIsSelectContentDialogOpen(false);
        setContentData({
          title: "",
          description: "",
          file: null,
          textContent: "",
          contentType: "",
        });
      } else {
        toast.error(response?.data?.message || "Failed to add content");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to add content");
    } finally {
      setAllLoading((prev) => ({ ...prev, uploadLoading: false }));
    }
  };

  // fetch all media
  const getAllMedia = async () => {
    setAllLoading((prev) => ({ ...prev, fetchLoading: true }));
    try {
      const response = await appServiceInstance.getAllMedia();
      if (response?.status === 200) {
        setAllMedia(response?.data?.data?.files);
        console.log(response?.data?.data.files);
      } else {
        toast.error(response?.data?.message || "Failed to fetch media");
      }
    } catch (error: any) {
      toast.error(error?.message || "Failed to fetch media");
    } finally {
      setAllLoading((prev) => ({ ...prev, fetchLoading: false }));
    }
  };

  // fetch  media
  useEffect(() => {
    setIsMounted(true);
    if (!allLoading.uploadLoading) {
      getAllMedia();
    }
  }, [allLoading.uploadLoading]);

  const renderGallery = (type: string) => {
    const filtered = allMedia?.filter((item: any) => item?.fileType === type);
    if (filtered?.length === 0) {
      return <p className="text-lg">No {type} content available.</p>;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        {filtered.map((item: any, index: number) => (
          <div key={index} className="w-90 h-80 ">
            <div
              className={`bg-gray-50 p-3 flex cursor-pointer rounded-2xl shadow-2xl  ${openSelectedMedia === item?._id
                ? "absolute left-1/2 translate-y-1 border border-gray-200 -translate-x-1/2 h-[580px] w-[980px] z-10 justify-between itmes-center"
                : "relative flex-col gap-1"
                } 
                ${item?.fileType === "text" ? "w-[280px] " : ""}
                `}
              onClick={() => {
                // Only set these values if we're opening the media (not already open)
                if (openSelectedMedia !== item?._id) {
                  setOpenSelectedMedia(item?._id);
                  // Initialize updateData with the current item's values
                  setUpdateData({
                    title: item?.title || "",
                    description: item?.description || "",
                    alternativeText: item?.alternativeText || "",
                    textContent: item?.textContent || "",
                  });
                  console.log("Setting updateData:", {
                    title: item?.title || "",
                    description: item?.description || "",
                    alternativeText: item?.alternativeText || "",
                    textContent: item?.textContent || "",
                  });
                }
              }}
            >
              {item?.fileType !== "text" && (
                <div
                  className={` ${openSelectedMedia === item?._id
                    ? "w-[45%] h-full p-2"
                    : "w-full h-48"
                    }  bg-gray-200 flex justify-center items-center rounded-xl overflow-hidden`}
                >
                  {type === "image" && (
                    <img
                      className="w-full h-full object-cover rounded-xl"
                      src={item?.downloadUrl}
                      alt="media"
                    />
                  )}
                  {type === "video" && (
                    <video
                      className="w-full h-full object-cover rounded-xl"
                      src={item?.downloadUrl}
                      controls
                    />
                  )}

                  {type === "audio" && (
                    // <audio
                    //   className="w-full object-cover"
                    //   src={item?.downloadUrl}
                    //   controls
                    // />
                    // <Waveform src="https://www.mfiles.co.uk/mp3-downloads/gs-cd-track2.mp3" />
                    <Waveform src={item?.downloadUrl} />

                  )}
                </div>
              )}

              {openSelectedMedia === item?._id ? (
                <div
                  className={` ${item?.fileType === "text" ? "w-full" : "w-1/2"
                    } w-1/2 h-full flex flex-col relative items-center justify-center`}
                >
                  <div
                    className="absolute top-0 cursor-pointer right-0 flex justify-end z-50 bg-custom-secondary p-1 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      setOpenSelectedMedia("");
                    }}
                  >
                    <X />
                  </div>
                  <span className="text-2xl font-bold font-custom-sans">Edit Media</span>
                  <div className="w-full px-8 py-8 flex flex-col gap-5">
                    <InputWithLabel
                      id="title"
                      label="Title"
                      placeholder="Enter title"
                      type="text"
                      value={updateData.title}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setUpdateData((prev) => {
                          const updated = {
                            ...prev,
                            title: newValue,
                          };
                          console.log("Updated title to:", newValue);
                          console.log("New updateData state:", updated);
                          return updated;
                        });
                      }}
                    />
                    <InputWithLabel
                      id="description"
                      label="Description"
                      placeholder="Enter description"
                      type="text"
                      value={updateData.description}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setUpdateData((prev) => {
                          const updated = {
                            ...prev,
                            description: newValue,
                          };
                          console.log("Updated description to:", newValue);
                          console.log("New updateData state:", updated);
                          return updated;
                        });
                      }}
                    />
                    {item?.fileType === "text" && (
                      <InputWithLabel
                        id="contentText"
                        label="Text"
                        placeholder="Enter text"
                        type="text"
                        value={updateData.textContent}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setUpdateData((prev) => {
                            const updated = {
                              ...prev,
                              textContent: newValue,
                            };
                            console.log("Updated alternativeText to:", newValue);
                            console.log("New updateData state:", updated);
                            return updated;
                          });
                        }}
                      />
                    )}
                    <InputWithLabel
                      id="alternativeText"
                      label="Alternative Text"
                      placeholder="Enter alternative text"
                      type="text"
                      value={updateData.alternativeText}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setUpdateData((prev) => {
                          const updated = {
                            ...prev,
                            alternativeText: newValue,
                          };
                          console.log("Updated alternativeText to:", newValue);
                          console.log("New updateData state:", updated);
                          return updated;
                        });
                      }}
                    />
                  </div>
                  <div className={`w-full h-full flex items-center justify-end gap-3 p-5`}>
                    <Button
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenSelectedMedia("");
                      }}
                      disabled={updateLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpdateMedia(item?._id);
                      }}
                      isLoading={updateLoading}
                      disabled={updateLoading}
                    >
                      Update
                    </Button>
                  </div>
                </div>
              ) : item?.fileType !== "text" ? (
                <div className="">
                  <div className="flex flex-col gap-2">
                    <div className="flex flex-col">
                      <span className="text-xl font-bold">{item?.title}</span>
                      <p className="text-xs text-gray-700">
                        {item?.description}
                      </p>
                      <p className="text-xs text-orange-500">
                        {(item?.fileSize / 1024).toFixed(2)} KB
                      </p>
                    </div>
                    <span className="text-sm text-red-600">
                      {item?.createdAt.slice(0, 10)}
                    </span>
                  </div>
                  <div className="absolute bottom-2 right-2 p-2 rounded-full shadow-2xl bg-red-100 cursor-pointer">
                    <Trash2
                      size={20}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedMediaId(item?._id);
                        setOpenDeleteDialog(true);
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-col p-4 relative italic font-semibold font-custom-sans">
                  <img src={'/img/double-quotes.png'} className="h-10 w-10 absolute top-0 left-0" />
                  <span className="text-base text-gray-700 pl-3">
                    {item?.textContent}
                  </span>
                  <img src={'/img/double-quotes.png'} className="h-10 w-10 absolute bottom-0 rotate-180 right-0" />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Add this function to handle media updates
  const handleUpdateMedia = async (id: string) => {
    if (!id) {
      toast.error("Media ID is missing");
      return;
    }

    // Log the current updateData state before sending
    console.log("Current updateData before update:", updateData);

    setUpdateLoading(true);
    try {
      const payload = {
        title: updateData.title,
        description: updateData.description,
        alternativeText: updateData.alternativeText,
        textContent: updateData.textContent,
      };

      console.log("Updating media with ID:", id);
      console.log("Update payload:", payload);

      const response = await appServiceInstance.updateMedia(id, payload);
      if (response?.status === 200) {
        toast.success("Media updated successfully!");
        setOpenSelectedMedia("");
        // Reset updateData after successful update
        setUpdateData({
          title: "",
          description: "",
          alternativeText: "",
          textContent: "",
        });
        // Refresh the media list
        getAllMedia();
      } else {
        toast.error(response?.data?.message || "Failed to update media");
      }
    } catch (error) {
      console.error("Error updating media:", error);
      toast.error("Failed to update media");
    } finally {
      setUpdateLoading(false);
    }
  };

  // delete media
  const handleDeleteMedia = async (id: string) => {
    if (!id) {
      toast.error("Media ID is missing");
      return;
    }

    setAllLoading((prev) => ({ ...prev, deleteLoading: true }));
    try {
      const response = await appServiceInstance.deleteMedia(id);
      if (response?.status === 200) {
        toast.success("Media deleted successfully!");
        // Refresh the media list
        setOpenDeleteDialog(false);
        getAllMedia();
      } else {
        toast.error(response?.data?.message || "Failed to delete media");
      }
    } catch (error) {
      console.error("Error deleting media:", error);
      toast.error("Failed to delete media");
    } finally {
      setAllLoading((prev) => ({ ...prev, deleteLoading: false }));
    }
  };

  return (
    <div className="p-4 relative">
      <div className="flex items-center justify-between">
        <CustomTabs activeTab={activeTab} setActiveTab={setActiveTab} />
        <button
          className="flex gap-3 cursor-pointer bg-custom-primary text-white px-3 py-3 rounded-3xl w-fit !m-0"
          onClick={() => setIsSelectContentDialogOpen(true)}
        >
          Add
          <CirclePlus size={25} />
        </button>
      </div>

      {allLoading.fetchLoading ? (
        <div className="flex justify-center items-center h-96">
          <SpinnerCustom />
        </div>
      ) : isMounted && allMedia?.length === 0 ? (
        <div className="w-full flex justify-center py-10">
          <p className="text-2xl font-bold text-center">No Media</p>
        </div>
      ) : (
        <div className="p-4">
          <h2 className="text-2xl font-bold mb-4 capitalize">
            {activeTab} Gallery
          </h2>
          {renderGallery(activeTab)}
        </div>
      )}

      {/* Select Content Dialog */}
      <SelectContentDialog
        open={isSelectContentDialogOpen}
        handleClose={() => setIsSelectContentDialogOpen(false)}
        selectedContenetType={selectedContentType}
        setSelectedContentType={setSelectedContentType}
        selectedFile={selectedFile}
        setSelectedFile={setSelectedFile}
        setContentData={setContentData}
        handleAddMedia={handleAddMedia}
        isLoading={allLoading.uploadLoading}
        fileInputRef={fileInputRef}
      />

      <AlertDialog
        open={openDeleteDialog}
        handleClose={() => setOpenDeleteDialog(false)}
        handleConfirm={() => handleDeleteMedia(selectedMediaId)}
        confirmText="Delete"
        cancelText="Cancel"
        title="Delete Media"
        description="Are you sure you want to delete this media?"
      />
    </div>
  );
};

export default Page;
