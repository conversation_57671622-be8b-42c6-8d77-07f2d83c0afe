'use client';
import Button from '@/app/components/ui/Button';
import InputWith<PERSON>abel from '@/app/components/ui/InputWithLabel'
import appServiceInstance from '@/app/services/AppServices';
import React, { useRef, useState } from 'react'
import { toast } from 'sonner';
import { z } from 'zod';

const updatePasswordSchema = z.object({
  currentPassword: z.string().min(6, "Password must be at least 6 characters"),
  newPassword: z.string()
    .min(6, "Password must be at least 6 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  confirmPassword: z.string().min(6, "Password must be at least 6 characters"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "New password must be different from current password",
  path: ["newPassword"],
});

type UpdatePasswordData = z.infer<typeof updatePasswordSchema>;

const page = () => {
  // updatePasswordStates
  const [updatePasswordData, setUpdatePasswordData] = useState<UpdatePasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [updatePasswordError, setUpdatePasswordError] = useState<Partial<UpdatePasswordData>>({});

  // Add these state variables and functions
  const [updatePasswordLoading, setUpdatePasswordLoading] = useState(false);
  const passwordFormRef = useRef<HTMLFormElement>(null);

  // Add this function to handle password input changes
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setUpdatePasswordData((prev) => ({
      ...prev,
      [id]: value,
    }));

    // Clear error when user types
    if (updatePasswordError[id as keyof UpdatePasswordData]) {
      setUpdatePasswordError((prev) => ({
        ...prev,
        [id]: undefined,
      }));
    }
  };

  // Add this function to handle password field blur events
  const handlePasswordBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;

    try {
      // Validate individual field
      if (id === 'currentPassword') {
        updatePasswordSchema.shape.currentPassword.parse(value);
      } else if (id === 'newPassword') {
        updatePasswordSchema.shape.newPassword.parse(value);

        // Check if new password matches confirm password
        if (updatePasswordData.confirmPassword &&
          updatePasswordData.confirmPassword !== value) {
          throw new z.ZodError([{
            code: "custom",
            message: "Passwords do not match",
            path: ["confirmPassword"]
          }]);
        }

        // Check if new password is same as current password
        if (updatePasswordData.currentPassword &&
          updatePasswordData.currentPassword === value) {
          throw new z.ZodError([{
            code: "custom",
            message: "New password must be different from current password",
            path: ["newPassword"]
          }]);
        }
      } else if (id === 'confirmPassword') {
        updatePasswordSchema.shape.confirmPassword.parse(value);

        // Check if passwords match
        if (updatePasswordData.newPassword &&
          updatePasswordData.newPassword !== value) {
          throw new z.ZodError([{
            code: "custom",
            message: "Passwords do not match",
            path: ["confirmPassword"]
          }]);
        }
      }

      setUpdatePasswordError((prev) => ({ ...prev, [id]: undefined }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setUpdatePasswordError((prev) => ({
          ...prev,
          [id]: error.errors[0].message
        }));
      }
    }
  };

  // Add this function to handle password update submission
  const handleUpdatePassword = async () => {
    try {
      setUpdatePasswordLoading(true);

      // Validate all fields
      updatePasswordSchema.parse(updatePasswordData);

      // Submit to server
      const response = await appServiceInstance.updatePassword({
        currentPassword: updatePasswordData.currentPassword,
        newPassword: updatePasswordData.newPassword,
        confirmNewPassword: updatePasswordData.confirmPassword
      });

      if (response.status === 200) {
        toast.success("Password updated successfully");

        // Reset form
        setUpdatePasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        setUpdatePasswordError({});
      } else {
        toast.error(response.data?.message || "Failed to update password");
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path[0];
          if (path) {
            newErrors[path] = err.message;
          }
        });
        setUpdatePasswordError(newErrors);
      } else {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    } finally {
      setUpdatePasswordLoading(false);
    }
  };

  return (
    <div>

      <div className=" bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white relative rounded-xl p-6 py-[30px] w-[90%] max-w-lg">
          <h2 className="text-[18px] md:text-[24px] font-conthrax font-bold mb-2 text-center">Update Your Password</h2>
          <p className="text-[14px] md:text-[16px] mb-4 text-center text-gray-600">
            Enter your current password and a new password below
          </p>

          {/* <X className="absolute top-3 right-3 cursor-pointer bg-gray-200 hover:bg-red-200 rounded-full p-1"
            size={30}
            onClick={() => setShowPopup(false)} /> */}

          <form ref={passwordFormRef} className="space-y-5">
            <div className="relative">
              <InputWithLabel
                id="currentPassword"
                label="Current Password"
                type={"password"}
                placeholder="Enter current password"
                value={updatePasswordData.currentPassword}
                onChange={handlePasswordChange}
                onBlur={handlePasswordBlur}
                error={updatePasswordError.currentPassword}
              />

            </div>

            <div className="relative">
              <InputWithLabel
                id="newPassword"
                label="New Password"
                type={"password"}
                placeholder="Enter new password"
                value={updatePasswordData.newPassword}
                onChange={handlePasswordChange}
                onBlur={handlePasswordBlur}
                error={updatePasswordError.newPassword}
              />

            </div>

            <div className="relative">
              <InputWithLabel
                id="confirmPassword"
                label="Confirm Password"
                type={"password"}
                placeholder="Confirm new password"
                value={updatePasswordData.confirmPassword}
                onChange={handlePasswordChange}
                onBlur={handlePasswordBlur}
                error={updatePasswordError.confirmPassword}
              />

            </div>
          </form>

          <div className="flex justify-center space-x-2 mt-6">
            <Button
              onClick={handleUpdatePassword}
              isLoading={updatePasswordLoading}
            >
              Update Password
            </Button>
          </div>

          {/* Password requirements */}
          <div className="mt-4 text-xs text-gray-500">
            <p className="font-semibold mb-1">Password requirements:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>At least 6 characters long</li>
              <li>At least one uppercase letter</li>
              <li>At least one lowercase letter</li>
              <li>At least one number</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default page