'use client';
import InputWithLabel from '@/app/components/ui/InputWithLabel'
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css'; 
import moment from 'moment';
import React, { useState } from 'react'

const page = () => {
  const [selectedDate, setSelectedDate] = useState<moment.Moment | null>(null);
  const handleDateChange = (value: moment.Moment | string) => {
    if (moment.isMoment(value)) {
      // Set to January 1st of the selected year to standardize
      const yearOnly = moment().year(value.year()).startOf('year');
      setSelectedDate(yearOnly);
      console.log("Selected year:", yearOnly.format('YYYY'));
    } else {
      setSelectedDate(null);
    }
  };

  return (
    <div className='flex flex-col w-full justify-center items-center'>
      <div className='text-2xl font-semibold'>Enter User Details</div>
      <div className='flex gap-2 w-[80%] border rounded-3xl p-5'>
        <div className='w-1/2'>
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
          <InputWithLabel id='tagLine' label='Tag Line' type='text' placeholder='Enter your Tag Line' className='w-full' />
          <InputWithLabel id='aboutMe' label='About Me' type='text' placeholder='About Me' className='w-full' />

          <div>
            <label className='font-semibold text-sm'>Date of Birth</label>
            <Datetime
              value={selectedDate ? selectedDate : undefined}
              onChange={handleDateChange}
              className="custom-datetime-input"
              timeFormat={false}
              dateFormat="DD/MM/YYYY"
              closeOnSelect={true}
              inputProps={{
                placeholder: "--Select a DOB --",
                className: "w-full border border-gray-300 border-b py-2 px-4 rounded-3xl"
              }}
            />
          </div>

          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
        </div>
        <div className='w-1/2'>
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
          <InputWithLabel id='name' label='Name' type='text' placeholder='Enter your Name' className='w-full' />
        </div>
      </div>
    </div>
  )
}

export default page