"use client";

import { redirect, useRouter } from "next/navigation";
import Button from "./components/ui/Button";
import { useEffect } from "react";

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    redirect("/dashboard");
  },[])

  return (
    <div>
      <div className="text-3xl font-bold">Home</div>
      <div className="flex w-full justify-center">
        <Button className="w-fit" onClick={() => router.push("/dashboard")}>dashboard</Button>
      </div>
    </div>
  );
}
