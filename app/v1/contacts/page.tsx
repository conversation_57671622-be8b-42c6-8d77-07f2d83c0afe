'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, ChevronLeft, Plus, Mail, Check, X, Shield, UserPlus, Search, Filter, MoreVertical, Edit2, Trash2, Send, CheckCircle, AlertCircle } from 'lucide-react';

const TrustedContactsPage = () => {
  const [activeSection, setActiveSection] = useState('settings');
  const [activeSubSection, setActiveSubSection] = useState('contacts');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [newContact, setNewContact] = useState({ name: '', email: '', role: '' });

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'gallery', label: 'Gallery', icon: Image },
    { id: 'slideshow', label: 'SlideShow', icon: Play },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  const [contacts, setContacts] = useState([
    { 
      id: 1, 
      name: 'Hi There', 
      email: '<EMAIL>', 
      status: 'pending',
      role: 'Family Member',
      addedDate: '2025-01-15',
      avatar: null
    },
    { 
      id: 2, 
      name: 'HDFC', 
      email: '<EMAIL>', 
      status: 'pending',
      role: 'Financial Advisor',
      addedDate: '2025-01-14',
      avatar: null
    },
    { 
      id: 3, 
      name: 'My Third', 
      email: '<EMAIL>', 
      status: 'pending',
      role: 'Legal Representative',
      addedDate: '2025-01-13',
      avatar: null
    }
  ]);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'inactive':
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
      default:
        return null;
    }
  };

  const getStatusStyle = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700';
      case 'inactive':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const handleDeleteContact = (id) => {
    setContacts(contacts.filter(contact => contact.id !== id));
  };

  const handleAddContact = () => {
    const newContactData = {
      id: contacts.length + 1,
      ...newContact,
      status: 'pending',
      addedDate: new Date().toISOString().split('T')[0],
      avatar: null
    };
    setContacts([...contacts, newContactData]);
    setNewContact({ name: '', email: '', role: '' });
    setShowAddModal(false);
  };

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch = contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contact.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || contact.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-600 to-blue-800 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
          {/* Logo Section */}
          <div className="p-6 relative">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="absolute -right-3 top-8 w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-600 hover:bg-gray-100 transition-colors duration-200 shadow-lg z-10"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
            <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="12" fill="#2563EB"/>
                  <circle cx="70" cy="40" r="12" fill="#2563EB"/>
                  <path d="M 30 65 Q 50 80 70 65" stroke="#2563EB" strokeWidth="3" fill="none"/>
                </svg>
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-semibold text-white">My Life</h1>
                  <p className="text-sm text-blue-200">My Happiness</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="px-4">
            {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-200 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
            <div className="space-y-2">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => {
                      setActiveSection(item.id);
                      if (item.id === 'settings') setActiveSubSection('contacts');
                    }}
                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-blue-100 hover:bg-white/10 hover:text-white'
                    }`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <item.icon className="w-5 h-5" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                  
                  {/* Submenu */}
                  {!sidebarCollapsed && item.subItems && activeSection === item.id && (
                    <div className="ml-4 mt-1 space-y-1">
                      {item.subItems.map((subItem) => (
                        <button
                          key={subItem.id}
                          onClick={() => setActiveSubSection(subItem.id)}
                          className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200 ${
                            activeSubSection === subItem.id
                              ? 'bg-white/20 text-white'
                              : 'text-blue-200 hover:bg-white/10 hover:text-white'
                          }`}
                        >
                          <subItem.icon className="w-4 h-4" />
                          <span className="text-sm">{subItem.label}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200`}>
              <LogOut className="w-5 h-5" />
              {!sidebarCollapsed && <span className="font-medium">Logout</span>}
            </button>
          </nav>

          {/* Theme Toggle */}
          <div className="absolute bottom-6 left-6">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center cursor-pointer hover:bg-white/30 transition-colors">
              <span className="text-white text-sm font-bold">N</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Trusted Contacts</h1>
              <p className="text-gray-600 mt-1">Manage your trusted contacts who can access your information</p>
            </div>
            <div className="flex items-center space-x-3 bg-white px-5 py-2.5 rounded-2xl shadow-md">
              <div className="relative">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">TU</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-800">TEST USER</p>
                <p className="text-xs text-gray-500">legacy creator</p>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            {/* Content Header */}
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800">Add Trusted Contacts</h2>
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors shadow-md"
              >
                Add
                <Plus className="w-5 h-5" />
              </button>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <div className="flex-1 relative">
                <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="text"
                  placeholder="Search contacts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedFilter}
                  onChange={(e) => setSelectedFilter(e.target.value)}
                  className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>

            {/* Contacts Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredContacts.map((contact) => (
                <div key={contact.id} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        {contact.avatar ? (
                          <img src={contact.avatar} alt={contact.name} className="w-full h-full rounded-full object-cover" />
                        ) : (
                          <User className="w-8 h-8 text-blue-600" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800 text-lg">{contact.name}</h3>
                        <p className="text-sm text-gray-500">{contact.role}</p>
                      </div>
                    </div>
                    <button className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <MoreVertical className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                    </button>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{contact.email}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-700">Status:</span>
                        <span className={`flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${getStatusStyle(contact.status)}`}>
                          {getStatusIcon(contact.status)}
                          {contact.status.toUpperCase()}
                        </span>
                      </div>
                    </div>

                    <div className="text-xs text-gray-400">
                      Added on {new Date(contact.addedDate).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex gap-2 mt-6">
                    {contact.status === 'pending' && (
                      <button className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg font-medium hover:bg-blue-100 transition-colors text-sm">
                        <Send className="w-4 h-4" />
                        Resend Invite
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteContact(contact.id)}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-red-50 text-red-600 rounded-lg font-medium hover:bg-red-100 transition-colors text-sm"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Empty State */}
            {filteredContacts.length === 0 && (
              <div className="text-center py-12">
                <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">No contacts found</h3>
                <p className="text-gray-400">Try adjusting your search or filter criteria</p>
              </div>
            )}

            {/* Info Section */}
            <div className="mt-12 bg-blue-50 rounded-xl p-6">
              <div className="flex items-start gap-4">
                <Shield className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">About Trusted Contacts</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Trusted contacts are individuals you authorize to access your important documents and information when needed. 
                    They will receive an invitation email and must accept it to become active.
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li className="flex items-start gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                      <span>Contacts can only access information you specifically share with them</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                      <span>You can revoke access at any time</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                      <span>All activities are logged for your security</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Contact Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-gray-800">Add New Contact</h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                <input
                  type="text"
                  value={newContact.name}
                  onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
                  placeholder="Enter contact's full name"
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  value={newContact.email}
                  onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
                  placeholder="Enter contact's email"
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Role/Relationship</label>
                <select
                  value={newContact.role}
                  onChange={(e) => setNewContact({ ...newContact, role: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a role</option>
                  <option value="Family Member">Family Member</option>
                  <option value="Legal Representative">Legal Representative</option>
                  <option value="Financial Advisor">Financial Advisor</option>
                  <option value="Healthcare Proxy">Healthcare Proxy</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex gap-3">
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddContact}
                disabled={!newContact.name || !newContact.email || !newContact.role}
                className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Send Invitation
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrustedContactsPage;