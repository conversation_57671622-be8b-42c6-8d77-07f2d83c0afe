'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, Calendar, Camera, Mail, Phone, Tag, Info, Sparkles, Video, FileText, Music, Plus, Search, Filter, Grid, List, Download, Share2, Trash2, Eye, Heart, MoreVertical, X, Upload, ChevronLeft, Menu, Pause, Volume2 } from 'lucide-react';

const GalleryPage = () => {
  const [activeSection, setActiveSection] = useState('gallery');
  const [activeTab, setActiveTab] = useState('image');
  const [selectedItems, setSelectedItems] = useState([]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newItem, setNewItem] = useState({ title: '', file: null });

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'gallery', label: 'Gallery', icon: Image, active: true },
    { id: 'slideshow', label: 'SlideShow', icon: Play },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  const tabs = [
    { id: 'image', label: 'Image', icon: Image },
    { id: 'video', label: 'Video', icon: Video },
    { id: 'text', label: 'Text', icon: FileText },
    { id: 'audio', label: 'Audio', icon: Music }
  ];

  const galleryItems = {
    image: [
      { id: 1, title: 'Sunset Photography', type: 'image', size: '2.45 MB', date: '2025-06-03', views: 234, likes: 45 },
      { id: 2, title: 'Mountain View', type: 'image', size: '3.21 MB', date: '2025-06-02', views: 567, likes: 89 },
      { id: 3, title: 'City Skyline', type: 'image', size: '1.87 MB', date: '2025-06-01', views: 123, likes: 23 },
      { id: 4, title: 'Nature Walk', type: 'image', size: '2.93 MB', date: '2025-05-30', views: 890, likes: 156 },
    ],
    video: [
      { id: 5, title: 'Travel Vlog 2025', type: 'video', duration: '10:45', size: '124.5 MB', date: '2025-06-03', views: 1234, likes: 245 },
      { id: 6, title: 'Tutorial Series', type: 'video', duration: '05:30', size: '68.2 MB', date: '2025-06-01', views: 789, likes: 123 },
    ],
    text: [
      { id: 7, title: 'My Journey to Success', type: 'text', excerpt: 'This is the story of how I transformed my life...', date: '2025-06-03', views: 456, likes: 78 },
      { id: 8, title: '10 Tips for Better Living', type: 'text', excerpt: 'Discover the secrets to a happier and more fulfilling life...', date: '2025-06-02', views: 892, likes: 167 },
    ],
    audio: [
      { id: 9, title: 'Meditation Music', type: 'audio', duration: '15:00', size: '12.3 MB', date: '2025-06-03', plays: 342 },
      { id: 10, title: 'Podcast Episode 1', type: 'audio', duration: '45:30', size: '41.2 MB', date: '2025-06-01', plays: 567 },
    ]
  };

  const handleSelectItem = (id) => {
    setSelectedItems(prev => 
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const handleViewItem = (item) => {
    setSelectedItem(item);
    setShowViewModal(true);
  };

  const handleAddNew = () => {
    setShowAddModal(true);
    setNewItem({ title: '', file: null });
  };

  const handleSubmitNew = () => {
    // Handle submission logic here
    setShowAddModal(false);
    setNewItem({ title: '', file: null });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-sky-50 to-indigo-50">
      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-900 to-indigo-900 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
          {/* Logo Section */}
          <div className="p-6 border-b border-blue-800/30 backdrop-blur-sm relative">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="absolute -right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors duration-200 shadow-lg"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
            <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
              <div className="w-14 h-14 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="15" fill="white" opacity="0.9"/>
                  <circle cx="70" cy="40" r="15" fill="white" opacity="0.9"/>
                  <path d="M 30 70 Q 50 85 70 70" stroke="white" strokeWidth="3" fill="none" opacity="0.9"/>
                </svg>
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-semibold text-white">My Life</h1>
                  <p className="text-sm text-blue-200">My Happiness</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-4">
            {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-300 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
            <div className="space-y-1">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]'
                        : 'text-blue-200 hover:bg-blue-800/30 hover:text-white'
                    }`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <item.icon className="w-5 h-5" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-200 hover:text-white hover:bg-red-600/20 rounded-xl transition-all duration-200 group`}>
              <LogOut className="w-5 h-5 group-hover:text-red-400" />
              {!sidebarCollapsed && <span className="font-medium">Logout</span>}
            </button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                Gallery
              </h1>
              <p className="text-gray-600 mt-1">Manage your media collection</p>
            </div>
            <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-5 py-2.5 rounded-2xl shadow-lg border border-blue-100">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">TU</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-800">TEST USER</p>
                <p className="text-xs text-blue-600 font-medium">legacy creator</p>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl p-6 mb-6 border border-blue-100">
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                        : 'text-gray-600 hover:bg-blue-50 hover:text-blue-600'
                    }`}
                  >
                    <tab.icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </div>
              <button 
                onClick={handleAddNew}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <span>Add</span>
                <Plus className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content Area */}
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl p-6 border border-blue-100">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">{tabs.find(t => t.id === activeTab)?.label} Gallery</h2>
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-10 pr-4 py-2 bg-blue-50/50 border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                </div>
                <button className="p-2 text-gray-600 hover:bg-blue-50 rounded-lg transition-colors duration-200">
                  <Filter className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Gallery Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleryItems[activeTab]?.map((item) => (
                <div key={item.id} className="group relative">
                  {/* Image Gallery */}
                  {activeTab === 'image' && (
                    <div 
                      className="bg-gray-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] cursor-pointer"
                      onClick={() => handleViewItem(item)}
                    >
                      <div className="h-64 bg-gradient-to-br from-blue-200 to-indigo-200 flex items-center justify-center">
                        <Image className="w-16 h-16 text-white/80" />
                      </div>
                      <div className="p-4 bg-white">
                        <h3 className="font-semibold text-gray-800 mb-2">{item.title}</h3>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <span>{item.size}</span>
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-1">
                              <Eye className="w-4 h-4" />
                              <span>{item.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Heart className="w-4 h-4" />
                              <span>{item.likes}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Video Gallery */}
                  {activeTab === 'video' && (
                    <div 
                      className="bg-gray-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] cursor-pointer"
                      onClick={() => handleViewItem(item)}
                    >
                      <div className="h-64 bg-gradient-to-br from-red-200 to-pink-200 flex items-center justify-center relative">
                        <Play className="w-16 h-16 text-white/80" />
                        <span className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                          {item.duration}
                        </span>
                      </div>
                      <div className="p-4 bg-white">
                        <h3 className="font-semibold text-gray-800 mb-2">{item.title}</h3>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <span>{item.size}</span>
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-1">
                              <Eye className="w-4 h-4" />
                              <span>{item.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Heart className="w-4 h-4" />
                              <span>{item.likes}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Text/Blog Gallery */}
                  {activeTab === 'text' && (
                    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] cursor-pointer border border-gray-100">
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <FileText className="w-8 h-8 text-blue-500" />
                          <span className="text-xs text-gray-500">{item.date}</span>
                        </div>
                        <h3 className="font-bold text-gray-800 text-lg mb-2">{item.title}</h3>
                        <p className="text-gray-600 text-sm mb-4 line-clamp-3">{item.excerpt}</p>
                        <div className="flex items-center justify-between">
                          <button className="text-blue-600 font-medium text-sm hover:text-blue-700">
                            Read More →
                          </button>
                          <div className="flex items-center space-x-3 text-sm text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Eye className="w-4 h-4" />
                              <span>{item.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Heart className="w-4 h-4" />
                              <span>{item.likes}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Audio Gallery */}
                  {activeTab === 'audio' && (
                    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                      <div className="p-6">
                        <div className="bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl p-6 mb-4 flex items-center justify-center">
                          <Music className="w-12 h-12 text-purple-600" />
                        </div>
                        <h3 className="font-semibold text-gray-800 mb-2">{item.title}</h3>
                        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                          <span>{item.duration}</span>
                          <span>{item.size}</span>
                        </div>
                        <div className="bg-gray-100 rounded-lg p-3 flex items-center space-x-3">
                          <button className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-colors">
                            <Play className="w-5 h-5 ml-0.5" />
                          </button>
                          <div className="flex-1">
                            <div className="h-1 bg-gray-300 rounded-full">
                              <div className="h-1 bg-blue-500 rounded-full w-1/3"></div>
                            </div>
                          </div>
                          <Volume2 className="w-5 h-5 text-gray-600" />
                        </div>
                        <div className="mt-3 flex items-center justify-center text-sm text-gray-500">
                          <Play className="w-4 h-4 mr-1" />
                          <span>{item.plays} plays</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* View Modal */}
      {showViewModal && selectedItem && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-800">{selectedItem.title}</h3>
              <button
                onClick={() => setShowViewModal(false)}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="p-6">
              {selectedItem.type === 'image' && (
                <div className="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl h-96 flex items-center justify-center">
                  <Image className="w-24 h-24 text-blue-400" />
                </div>
              )}
              {selectedItem.type === 'video' && (
                <div className="bg-gradient-to-br from-red-100 to-pink-100 rounded-2xl h-96 flex items-center justify-center">
                  <Play className="w-24 h-24 text-red-400" />
                </div>
              )}
              <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 mb-1">Size</p>
                  <p className="font-medium text-gray-800">{selectedItem.size}</p>
                </div>
                <div>
                  <p className="text-gray-500 mb-1">Date</p>
                  <p className="font-medium text-gray-800">{selectedItem.date}</p>
                </div>
                <div>
                  <p className="text-gray-500 mb-1">Views</p>
                  <p className="font-medium text-gray-800">{selectedItem.views}</p>
                </div>
                <div>
                  <p className="text-gray-500 mb-1">Likes</p>
                  <p className="font-medium text-gray-800">{selectedItem.likes}</p>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3">
              <button className="px-6 py-2.5 text-gray-600 bg-gray-100 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                <Download className="w-4 h-4 inline mr-2" />
                Download
              </button>
              <button className="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:shadow-lg transition-all">
                <Share2 className="w-4 h-4 inline mr-2" />
                Share
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-lg w-full">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-800">Add New {tabs.find(t => t.id === activeTab)?.label}</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">Title</label>
                <input
                  type="text"
                  value={newItem.title}
                  onChange={(e) => setNewItem({ ...newItem, title: e.target.value })}
                  placeholder={`Enter ${activeTab} title`}
                  className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">Upload File</label>
                <div className="border-2 border-dashed border-blue-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors">
                  <Upload className="w-12 h-12 text-blue-400 mx-auto mb-3" />
                  <p className="text-gray-600 mb-2">Drag and drop your file here or</p>
                  <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg font-medium hover:bg-blue-200 transition-colors">
                    Browse Files
                  </button>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-3">
              <button 
                onClick={() => setShowAddModal(false)}
                className="px-6 py-2.5 text-gray-600 bg-gray-100 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button 
                onClick={handleSubmitNew}
                className="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:shadow-lg transition-all"
              >
                Add {tabs.find(t => t.id === activeTab)?.label}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GalleryPage;