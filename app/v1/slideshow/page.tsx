'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, ChevronLeft, Menu, Plus, Eye, Edit2, Trash2, Grid, Pause, SkipBack, SkipForward, Maximize2, Volume2, X, Upload, Move, Copy, Save, Music, Type, Palette, Timer, ChevronUp, ChevronDown } from 'lucide-react';

const SlideshowPage = () => {
  const [activeSection, setActiveSection] = useState('slideshow');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showFullscreen, setShowFullscreen] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [slideshowData, setSlideshowData] = useState({
    title: '',
    description: '',
    transition: 'fade',
    duration: '3',
    music: null
  });
  const [editSlides, setEditSlides] = useState([]);

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'gallery', label: 'Gallery', icon: Image },
    { id: 'slideshow', label: 'SlideShow', icon: Play, active: true },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  const slides = [
    { id: 1, title: 'Mountain Vista', description: 'Breathtaking mountain landscape' },
    { id: 2, title: 'Waterfall Paradise', description: 'Majestic waterfall in green valley' },
    { id: 3, title: 'Forest Railway', description: 'Journey through the forest' },
    { id: 4, title: 'Alpine Peaks', description: 'Snow-capped mountain peaks' },
    { id: 5, title: 'Desert Sunset', description: 'Golden hour in the desert' },
    { id: 6, title: 'Ocean Waves', description: 'Peaceful ocean scenery' }
  ];

  const transitions = [
    { value: 'fade', label: 'Fade' },
    { value: 'slide', label: 'Slide' },
    { value: 'zoom', label: 'Zoom' },
    { value: 'flip', label: 'Flip' }
  ];

  const handlePrevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  const handleNextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleCreateSlideshow = () => {
    setShowCreateModal(true);
  };

  const handleEditSlideshow = () => {
    setEditSlides([...slides]);
    setShowEditModal(true);
  };

  const handleViewSlideshow = () => {
    setShowPreviewModal(true);
    setIsPlaying(true);
  };

  const moveSlideUp = (index) => {
    if (index > 0) {
      const newSlides = [...editSlides];
      [newSlides[index], newSlides[index - 1]] = [newSlides[index - 1], newSlides[index]];
      setEditSlides(newSlides);
    }
  };

  const moveSlideDown = (index) => {
    if (index < editSlides.length - 1) {
      const newSlides = [...editSlides];
      [newSlides[index], newSlides[index + 1]] = [newSlides[index + 1], newSlides[index]];
      setEditSlides(newSlides);
    }
  };

  const deleteSlide = (index) => {
    setEditSlides(editSlides.filter((_, i) => i !== index));
  };

  // Auto-play functionality
  React.useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        handleNextSlide();
      }, parseInt(slideshowData.duration) * 1000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, currentSlide, slideshowData.duration]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Modern Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-700 to-blue-900 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
          {/* Logo Section */}
          <div className="p-6 relative">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="absolute -right-3 top-8 w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-700 hover:bg-gray-100 transition-colors duration-200 shadow-lg z-10"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
            <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="12" fill="#3B82F6"/>
                  <circle cx="70" cy="40" r="12" fill="#3B82F6"/>
                  <path d="M 30 65 Q 50 80 70 65" stroke="#3B82F6" strokeWidth="3" fill="none"/>
                </svg>
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-semibold text-white">My Life</h1>
                  <p className="text-sm text-blue-200">My Happiness</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="px-4">
            {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-300 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
            <div className="space-y-2">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-blue-100 hover:bg-white/10 hover:text-white'
                    }`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <item.icon className="w-5 h-5" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200`}>
              <LogOut className="w-5 h-5" />
              {!sidebarCollapsed && <span className="font-medium">Logout</span>}
            </button>
          </nav>

          {/* Theme Toggle */}
          <div className="absolute bottom-6 left-6">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center cursor-pointer hover:bg-white/30 transition-colors">
              <span className="text-white text-sm font-bold">N</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-4xl font-bold text-gray-800">Slideshow</h1>
              <p className="text-gray-600 mt-2">Create and manage your photo presentations</p>
            </div>
            <div className="flex items-center space-x-4">
              <button 
                onClick={handleCreateSlideshow}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-xl font-medium shadow-lg hover:bg-blue-700 transition-all duration-300"
              >
                <Plus className="w-5 h-5" />
                <span>Create Your Slideshow</span>
              </button>
              <div className="flex items-center space-x-3 bg-white px-5 py-2.5 rounded-2xl shadow-md">
                <div className="relative">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold">TU</span>
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <div>
                  <p className="font-semibold text-gray-800">TEST USER</p>
                  <p className="text-xs text-gray-500">legacy creator</p>
                </div>
              </div>
            </div>
          </div>

          {/* Slideshow Container */}
          <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
            {/* Slideshow Viewer */}
            <div className="relative h-[500px] bg-gray-900">
              {/* Main Slide Display */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative w-full h-full">
                  {/* Slide Image Placeholder */}
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center">
                    <div className="text-center text-white">
                      <h2 className="text-6xl font-bold mb-4">{currentSlide + 1}</h2>
                      <h3 className="text-2xl font-semibold mb-2">{slides[currentSlide].title}</h3>
                      <p className="text-lg opacity-80">{slides[currentSlide].description}</p>
                    </div>
                  </div>

                  {/* Navigation Arrows */}
                  <button
                    onClick={handlePrevSlide}
                    className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  <button
                    onClick={handleNextSlide}
                    className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>

                  {/* Slide Counter */}
                  <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full">
                    <span className="text-white font-medium">{currentSlide + 1} / {slides.length}</span>
                  </div>
                </div>
              </div>

              {/* Controls Bar */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => setCurrentSlide(0)}
                      className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                    >
                      <SkipBack className="w-5 h-5" />
                    </button>
                    <button
                      onClick={togglePlayPause}
                      className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-all shadow-lg"
                    >
                      {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6 ml-0.5" />}
                    </button>
                    <button
                      onClick={() => setCurrentSlide(slides.length - 1)}
                      className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                    >
                      <SkipForward className="w-5 h-5" />
                    </button>
                  </div>
                  <div className="flex items-center space-x-4">
                    <button className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all">
                      <Volume2 className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => setShowFullscreen(true)}
                      className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                    >
                      <Maximize2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Thumbnail Strip */}
            <div className="bg-gray-100 p-6">
              <div className="flex items-center space-x-4 overflow-x-auto">
                {slides.map((slide, index) => (
                  <button
                    key={slide.id}
                    onClick={() => setCurrentSlide(index)}
                    className={`relative flex-shrink-0 w-40 h-24 rounded-xl overflow-hidden transition-all duration-300 ${
                      currentSlide === index ? 'ring-4 ring-blue-600 shadow-lg scale-105' : 'hover:shadow-md'
                    }`}
                  >
                    <div className="w-full h-full bg-gradient-to-br from-blue-300 to-purple-400 flex items-center justify-center">
                      <span className="text-white font-bold text-2xl">{index + 1}</span>
                    </div>
                    {currentSlide === index && (
                      <div className="absolute inset-0 bg-blue-600/20"></div>
                    )}
                  </button>
                ))}
              </div>

              {/* Progress Bar */}
              <div className="mt-6">
                <div className="h-2 bg-gray-300 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-600 transition-all duration-300"
                    style={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end mt-6 space-x-4">
                <button 
                  onClick={handleEditSlideshow}
                  className="flex items-center space-x-2 px-6 py-2.5 text-gray-600 bg-white border border-gray-300 rounded-xl font-medium hover:bg-gray-50 transition-all"
                >
                  <Edit2 className="w-4 h-4" />
                  <span>Edit Slideshow</span>
                </button>
                <button 
                  onClick={handleViewSlideshow}
                  className="flex items-center space-x-2 px-6 py-2.5 bg-blue-600 text-white rounded-xl font-medium shadow-lg hover:bg-blue-700 transition-all"
                >
                  <Eye className="w-4 h-4" />
                  <span>View Slide Show</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create Slideshow Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between sticky top-0 bg-white">
              <h3 className="text-2xl font-bold text-gray-800">Create New Slideshow</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div>
                <h4 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <Type className="w-5 h-5 mr-2 text-blue-600" />
                  Basic Information
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Slideshow Title</label>
                    <input
                      type="text"
                      value={slideshowData.title}
                      onChange={(e) => setSlideshowData({ ...slideshowData, title: e.target.value })}
                      placeholder="Enter slideshow title"
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea
                      value={slideshowData.description}
                      onChange={(e) => setSlideshowData({ ...slideshowData, description: e.target.value })}
                      placeholder="Enter slideshow description"
                      rows={3}
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                    />
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div>
                <h4 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <Settings className="w-5 h-5 mr-2 text-blue-600" />
                  Slideshow Settings
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Palette className="w-4 h-4 mr-1 text-gray-500" />
                      Transition Effect
                    </label>
                    <select
                      value={slideshowData.transition}
                      onChange={(e) => setSlideshowData({ ...slideshowData, transition: e.target.value })}
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    >
                      {transitions.map((transition) => (
                        <option key={transition.value} value={transition.value}>
                          {transition.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Timer className="w-4 h-4 mr-1 text-gray-500" />
                      Slide Duration (seconds)
                    </label>
                    <input
                      type="number"
                      value={slideshowData.duration}
                      onChange={(e) => setSlideshowData({ ...slideshowData, duration: e.target.value })}
                      min="1"
                      max="30"
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                  </div>
                </div>
              </div>

              {/* Media Upload */}
              <div>
                <h4 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <Image className="w-5 h-5 mr-2 text-blue-600" />
                  Add Images
                </h4>
                <div className="border-2 border-dashed border-blue-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors bg-blue-50/30">
                  <Upload className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2 font-medium">Drag and drop your images here</p>
                  <p className="text-sm text-gray-500 mb-4">or</p>
                  <button className="px-6 py-2.5 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors">
                    Browse Images
                  </button>
                  <p className="text-xs text-gray-500 mt-4">Supported formats: JPG, PNG, GIF (Max 10MB per image)</p>
                </div>
              </div>

              {/* Background Music */}
              <div>
                <h4 className="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <Music className="w-5 h-5 mr-2 text-blue-600" />
                  Background Music (Optional)
                </h4>
                <div className="border border-gray-200 rounded-xl p-4 bg-gray-50">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <Music className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-700">Choose Music File</span>
                  </button>
                  <p className="text-xs text-gray-500 mt-2">Supported formats: MP3, WAV (Max 20MB)</p>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex items-center justify-end space-x-4 sticky bottom-0 bg-white">
              <button 
                onClick={() => setShowCreateModal(false)}
                className="px-6 py-2.5 text-gray-600 bg-gray-100 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button className="px-8 py-2.5 bg-blue-600 text-white rounded-xl font-medium shadow-lg hover:bg-blue-700 transition-all">
                Create Slideshow
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Slideshow Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-6 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-800">Edit Slideshow</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto p-6">
              <div className="grid grid-cols-3 gap-6">
                {/* Slide List */}
                <div className="col-span-2">
                  <h4 className="text-lg font-semibold text-gray-700 mb-4">Slide Order</h4>
                  <div className="space-y-3">
                    {editSlides.map((slide, index) => (
                      <div
                        key={slide.id}
                        className="flex items-center space-x-3 bg-gray-50 p-4 rounded-xl border border-gray-200 hover:shadow-md transition-all"
                      >
                        <div className="flex-shrink-0 w-20 h-16 bg-gradient-to-br from-blue-300 to-purple-400 rounded-lg flex items-center justify-center">
                          <span className="text-white font-bold">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-800">{slide.title}</h5>
                          <p className="text-sm text-gray-500">{slide.description}</p>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <button
                            onClick={() => moveSlideUp(index)}
                            disabled={index === 0}
                            className="w-8 h-8 bg-white rounded-lg flex items-center justify-center hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                          >
                            <ChevronUp className="w-4 h-4 text-gray-600" />
                          </button>
                          <button
                            onClick={() => moveSlideDown(index)}
                            disabled={index === editSlides.length - 1}
                            className="w-8 h-8 bg-white rounded-lg flex items-center justify-center hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                          >
                            <ChevronDown className="w-4 h-4 text-gray-600" />
                          </button>
                        </div>
                        <button
                          onClick={() => deleteSlide(index)}
                          className="w-8 h-8 bg-red-50 rounded-lg flex items-center justify-center hover:bg-red-100 transition-all"
                        >
                          <Trash2 className="w-4 h-4 text-red-600" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <button className="mt-4 w-full py-3 border-2 border-dashed border-blue-300 rounded-xl text-blue-600 font-medium hover:border-blue-400 hover:bg-blue-50 transition-all">
                    <Plus className="w-5 h-5 inline mr-2" />
                    Add More Slides
                  </button>
                </div>

                {/* Settings Panel */}
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-700 mb-4">Slideshow Settings</h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Transition</label>
                        <select className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <option>Fade</option>
                          <option>Slide</option>
                          <option>Zoom</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                        <input
                          type="number"
                          defaultValue="3"
                          className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Music</label>
                        <button className="w-full px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-left hover:bg-gray-100 transition-colors">
                          <Music className="w-4 h-4 inline mr-2 text-gray-500" />
                          <span className="text-gray-600">Choose File</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-semibold text-gray-700 mb-4">Quick Actions</h4>
                    <div className="space-y-2">
                      <button className="w-full px-4 py-2 bg-blue-50 text-blue-700 rounded-lg font-medium hover:bg-blue-100 transition-colors">
                        <Copy className="w-4 h-4 inline mr-2" />
                        Duplicate Slideshow
                      </button>
                      <button className="w-full px-4 py-2 bg-gray-50 text-gray-700 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                        <Save className="w-4 h-4 inline mr-2" />
                        Save as Template
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex items-center justify-between">
              <button className="text-red-600 font-medium hover:text-red-700 transition-colors">
                Delete Slideshow
              </button>
              <div className="flex items-center space-x-4">
                <button 
                  onClick={() => setShowEditModal(false)}
                  className="px-6 py-2.5 text-gray-600 bg-gray-100 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button className="px-8 py-2.5 bg-blue-600 text-white rounded-xl font-medium shadow-lg hover:bg-blue-700 transition-all">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Slideshow Preview Modal */}
      {showPreviewModal && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col">
          {/* Header */}
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-6 z-10">
            <div className="flex items-center justify-between">
              <div className="text-white">
                <h3 className="text-2xl font-bold">Mountain Adventure</h3>
                <p className="text-white/80 mt-1">6 beautiful landscape photos</p>
              </div>
              <button
                onClick={() => {
                  setShowPreviewModal(false);
                  setIsPlaying(false);
                }}
                className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center">
            <div className="relative w-full h-full max-w-7xl mx-auto px-4">
              <div className="w-full h-full flex items-center justify-center">
                <div className="relative w-full h-[80vh]">
                  {/* Slide */}
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-600 rounded-2xl flex items-center justify-center">
                    <div className="text-center text-white">
                      <h2 className="text-8xl font-bold mb-6">{currentSlide + 1}</h2>
                      <h3 className="text-4xl font-semibold mb-4">{slides[currentSlide].title}</h3>
                      <p className="text-2xl opacity-80">{slides[currentSlide].description}</p>
                    </div>
                  </div>

                  {/* Navigation */}
                  <button
                    onClick={handlePrevSlide}
                    className="absolute left-8 top-1/2 -translate-y-1/2 w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                  >
                    <ChevronLeft className="w-8 h-8" />
                  </button>
                  <button
                    onClick={handleNextSlide}
                    className="absolute right-8 top-1/2 -translate-y-1/2 w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                  >
                    <ChevronRight className="w-8 h-8" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
            <div className="max-w-7xl mx-auto">
              {/* Progress Bar */}
              <div className="mb-4">
                <div className="h-1 bg-white/30 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-white transition-all duration-300"
                    style={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center justify-center space-x-6">
                <button
                  onClick={() => setCurrentSlide(0)}
                  className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                >
                  <SkipBack className="w-6 h-6" />
                </button>
                <button
                  onClick={togglePlayPause}
                  className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-all shadow-lg"
                >
                  {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-0.5" />}
                </button>
                <button
                  onClick={() => setCurrentSlide(slides.length - 1)}
                  className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
                >
                  <SkipForward className="w-6 h-6" />
                </button>
              </div>

              {/* Thumbnail Preview */}
              <div className="mt-6 flex items-center justify-center space-x-2">
                {slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-all ${
                      currentSlide === index 
                        ? 'w-8 bg-white' 
                        : 'bg-white/40 hover:bg-white/60'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fullscreen Modal */}
      {showFullscreen && (
        <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
          <button
            onClick={() => setShowFullscreen(false)}
            className="absolute top-4 right-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all"
          >
            <X className="w-6 h-6" />
          </button>
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center text-white">
              <h2 className="text-8xl font-bold mb-6">{currentSlide + 1}</h2>
              <h3 className="text-4xl font-semibold mb-4">{slides[currentSlide].title}</h3>
              <p className="text-2xl opacity-80">{slides[currentSlide].description}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SlideshowPage;