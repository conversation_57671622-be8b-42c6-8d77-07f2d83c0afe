'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, ChevronLeft, Check, X, Zap, Shield, Cloud, Mail, Headphones, FileText, Infinity, Star, TrendingUp, Award, Sparkles } from 'lucide-react';

const PlansPage = () => {
  const [activeSection, setActiveSection] = useState('plans');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [billingCycle, setBillingCycle] = useState('monthly');

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'gallery', label: 'Gallery', icon: Image },
    { id: 'slideshow', label: 'SlideShow', icon: Play },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard, active: true },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-700 to-blue-900 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
          {/* Logo Section */}
          <div className="p-6 relative">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="absolute -right-3 top-8 w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-700 hover:bg-gray-100 transition-colors duration-200 shadow-lg z-10"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
            <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="12" fill="#3B82F6"/>
                  <circle cx="70" cy="40" r="12" fill="#3B82F6"/>
                  <path d="M 30 65 Q 50 80 70 65" stroke="#3B82F6" strokeWidth="3" fill="none"/>
                </svg>
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-semibold text-white">My Life</h1>
                  <p className="text-sm text-blue-200">My Happiness</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="px-4">
            {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-300 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
            <div className="space-y-2">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-blue-100 hover:bg-white/10 hover:text-white'
                    }`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <item.icon className="w-5 h-5" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200`}>
              <LogOut className="w-5 h-5" />
              {!sidebarCollapsed && <span className="font-medium">Logout</span>}
            </button>
          </nav>

          {/* Theme Toggle */}
          <div className="absolute bottom-6 left-6">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center cursor-pointer hover:bg-white/30 transition-colors">
              <span className="text-white text-sm font-bold">N</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800">Plans</h1>
            <div className="flex items-center space-x-3 bg-white px-5 py-2.5 rounded-2xl shadow-md">
              <div className="relative">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">TU</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-800">TEST USER</p>
                <p className="text-xs text-gray-500">legacy creator</p>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="max-w-7xl mx-auto">
            {/* Title Section */}
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold text-gray-800 mb-3">Select a Plan That Suits You</h2>
              <p className="text-gray-600 text-lg">Start for free or unlock premium features with our paid plans</p>
            </div>

            {/* Billing Toggle */}
            <div className="flex justify-center mb-12">
              <div className="bg-gray-100 p-1 rounded-full inline-flex">
                <button
                  onClick={() => setBillingCycle('monthly')}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === 'monthly' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingCycle('yearly')}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all flex items-center gap-2 ${
                    billingCycle === 'yearly' 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Yearly 
                  <span className="bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">Save 15%</span>
                </button>
              </div>
            </div>

            {/* Plans Container */}
            <div className="grid lg:grid-cols-2 gap-8 mb-16">
              {/* Free Plan */}
              <div className="bg-white rounded-2xl shadow-lg p-8 relative hover:shadow-xl transition-shadow">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">Free Plan</h3>
                  <div className="mb-4">
                    <span className="text-5xl font-bold text-gray-800">$0</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                </div>

                <div className="mb-8">
                  <h4 className="font-semibold text-gray-700 mb-4">Features</h4>
                  <ul className="space-y-3">
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Create 1 Will</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Basic Digital Signature</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Limited Storage</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Email Support (Standard)</span>
                    </li>
                  </ul>
                </div>

                <button className="w-full py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors">
                  Start for Free
                </button>
              </div>

              {/* Premium Plan */}
              <div className="bg-white rounded-2xl shadow-lg p-8 relative hover:shadow-xl transition-shadow border-2 border-blue-600">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Recommended
                  </span>
                </div>
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">Premium Plan</h3>
                  <div className="mb-4">
                    <span className="text-5xl font-bold text-gray-800">$499</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                  {billingCycle === 'yearly' && (
                    <p className="text-green-600 text-sm">Save $898 per year</p>
                  )}
                </div>

                <div className="mb-8">
                  <h4 className="font-semibold text-gray-700 mb-4">Features</h4>
                  <ul className="space-y-3">
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Unlimited Wills</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Advanced Digital Signatures</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Secure Cloud Storage</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Priority Email & Chat Support</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-blue-600" />
                      <span>Legal Review (if applicable)</span>
                    </li>
                  </ul>
                </div>

                <button className="w-full py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors">
                  Upgrade to Premium
                </button>
              </div>
            </div>

            {/* Benefits Section */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Secure & Private</h4>
                <p className="text-sm text-gray-600">Your data is encrypted and protected</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">Trusted by Thousands</h4>
                <p className="text-sm text-gray-600">Join our growing community</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Headphones className="w-8 h-8 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">24/7 Support</h4>
                <p className="text-sm text-gray-600">We're here to help anytime</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlansPage;