'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, ChevronLeft, Plus, Calendar, FileText, Video, Music, Camera, Mic, Edit2, Trash2, X, ChevronUp, ChevronDown, Eye, Download, Share2, Heart, MessageCircle, Bookmark, MoreHorizontal, Sparkles, Star, MapPin, Tag } from 'lucide-react';

const TimelinePage = () => {
  const [activeSection, setActiveSection] = useState('timeline');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedYear, setSelectedYear] = useState(2023);
  const [viewMode, setViewMode] = useState('timeline'); // timeline, grid, story
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [newEvent, setNewEvent] = useState({
    heading: '',
    title: '',
    description: '',
    year: 2023,
    month: 1,
    type: 'text',
    content: '',
    location: '',
    tags: []
  });

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'gallery', label: 'Gallery', icon: Image },
    { id: 'slideshow', label: 'SlideShow', icon: Play },
    { id: 'timeline', label: 'Timeline', icon: Clock, active: true },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  const [timelineEvents, setTimelineEvents] = useState([
    {
      id: 1,
      year: 2020,
      month: 3,
      heading: 'Career Milestone',
      title: 'Started New Job at Tech Innovators',
      description: 'Joined as Senior Developer after 5 rounds of interviews',
      type: 'text',
      content: 'This was a major turning point in my career. After months of preparation and multiple interview rounds, I finally landed my dream job at Tech Innovators. The team is amazing and the projects are cutting-edge.',
      location: 'San Francisco, CA',
      tags: ['career', 'achievement', 'tech'],
      likes: 42,
      comments: 8,
      thumbnail: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400'
    },
    {
      id: 2,
      year: 2021,
      month: 6,
      heading: 'Personal Achievement',
      title: 'Graduation Day - Masters in Computer Science',
      description: 'Finally completed my Masters degree with distinction',
      type: 'image',
      content: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800',
      location: 'Stanford University',
      tags: ['education', 'milestone', 'proud'],
      likes: 156,
      comments: 23,
      thumbnail: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=400'
    },
    {
      id: 3,
      year: 2022,
      month: 7,
      heading: 'Travel Memory',
      title: 'Backpacking Through Europe',
      description: '30 days, 12 countries, countless memories',
      type: 'video',
      content: 'europe-trip.mp4',
      duration: '12:45',
      location: 'Europe',
      tags: ['travel', 'adventure', 'europe'],
      likes: 89,
      comments: 15,
      thumbnail: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400'
    },
    {
      id: 4,
      year: 2022,
      month: 11,
      heading: 'Family Celebration',
      title: 'Parents 30th Anniversary',
      description: 'A beautiful celebration with the whole family',
      type: 'image',
      content: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800',
      location: 'Home',
      tags: ['family', 'celebration', 'love'],
      likes: 78,
      comments: 12,
      thumbnail: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=400'
    },
    {
      id: 5,
      year: 2023,
      month: 2,
      heading: 'Creative Project',
      title: 'Launched My First Podcast',
      description: 'Tech Talk - discussing latest in AI and innovation',
      type: 'audio',
      content: 'podcast-ep1.mp3',
      duration: '45:30',
      location: 'Home Studio',
      tags: ['podcast', 'creative', 'tech'],
      likes: 67,
      comments: 19,
      thumbnail: 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400'
    },
    {
      id: 6,
      year: 2023,
      month: 8,
      heading: 'Adventure Sport',
      title: 'First Time Skydiving',
      description: 'Jumped from 15,000 feet - absolutely thrilling!',
      type: 'video',
      content: 'skydiving.mp4',
      duration: '5:23',
      location: 'Dubai, UAE',
      tags: ['adventure', 'sports', 'bucket-list'],
      likes: 234,
      comments: 45,
      thumbnail: 'https://images.unsplash.com/photo-1601024445121-e5b82f020549?w=400'
    },
    {
      id: 7,
      year: 2024,
      month: 1,
      heading: 'Life Update',
      title: 'Moved to a New City',
      description: 'Starting fresh in Seattle for new opportunities',
      type: 'text',
      content: 'After much deliberation, I decided to relocate to Seattle. The tech scene here is incredible, and I\'m excited about the new opportunities. Already loving the coffee culture and the beautiful Pacific Northwest!',
      location: 'Seattle, WA',
      tags: ['life-change', 'moving', 'new-beginnings'],
      likes: 92,
      comments: 27,
      thumbnail: 'https://images.unsplash.com/photo-1502175353174-a7a70e73b362?w=400'
    },
    {
      id: 8,
      year: 2025,
      month: 2,
      heading: 'Future Goals',
      title: 'Planning to Launch Startup',
      description: 'Working on AI-powered education platform',
      type: 'text',
      content: 'The business plan is ready, team is assembled, and we\'re ready to disrupt the education industry with our AI-powered personalized learning platform.',
      location: 'Silicon Valley',
      tags: ['startup', 'entrepreneur', 'future'],
      likes: 145,
      comments: 38,
      thumbnail: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=400'
    }
  ]);

  const years = [2020, 2021, 2022, 2023, 2024, 2025];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const categories = ['all', 'career', 'travel', 'family', 'education', 'creative', 'adventure'];

  const getEventsByYear = (year) => {
    return timelineEvents.filter(event => {
      const matchesYear = event.year === year;
      const matchesCategory = selectedCategory === 'all' || event.tags.includes(selectedCategory);
      return matchesYear && matchesCategory;
    });
  };

  const getContentIcon = (type) => {
    switch (type) {
      case 'text': return <FileText className="w-5 h-5" />;
      case 'image': return <Image className="w-5 h-5" />;
      case 'video': return <Video className="w-5 h-5" />;
      case 'audio': return <Music className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  const getContentColor = (type) => {
    switch (type) {
      case 'text': return 'bg-blue-500 text-white';
      case 'image': return 'bg-emerald-500 text-white';
      case 'video': return 'bg-purple-500 text-white';
      case 'audio': return 'bg-orange-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getGradientByType = (type) => {
    switch (type) {
      case 'text': return 'from-blue-400 to-blue-600';
      case 'image': return 'from-emerald-400 to-emerald-600';
      case 'video': return 'from-purple-400 to-purple-600';
      case 'audio': return 'from-orange-400 to-orange-600';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const handleAddEvent = () => {
    const newEventData = {
      id: timelineEvents.length + 1,
      ...newEvent,
      likes: 0,
      comments: 0,
      thumbnail: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400'
    };
    setTimelineEvents([...timelineEvents, newEventData]);
    setShowAddModal(false);
    setNewEvent({
      heading: '',
      title: '',
      description: '',
      year: 2023,
      month: 1,
      type: 'text',
      content: '',
      location: '',
      tags: []
    });
  };

  const handleDeleteEvent = (id) => {
    setTimelineEvents(timelineEvents.filter(event => event.id !== id));
  };

  const allEvents = timelineEvents.sort((a, b) => {
    if (a.year !== b.year) return b.year - a.year;
    return b.month - a.month;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-600 to-blue-800 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
          {/* Logo Section */}
          <div className="p-6 relative">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="absolute -right-3 top-8 w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-600 hover:bg-gray-100 transition-colors duration-200 shadow-lg z-10"
            >
              {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
            </button>
            <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="12" fill="#2563EB"/>
                  <circle cx="70" cy="40" r="12" fill="#2563EB"/>
                  <path d="M 30 65 Q 50 80 70 65" stroke="#2563EB" strokeWidth="3" fill="none"/>
                </svg>
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-semibold text-white">My Life</h1>
                  <p className="text-sm text-blue-200">My Happiness</p>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="px-4">
            {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-200 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
            <div className="space-y-2">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-white/20 text-white shadow-lg'
                        : 'text-blue-100 hover:bg-white/10 hover:text-white'
                    }`}
                    title={sidebarCollapsed ? item.label : ''}
                  >
                    <item.icon className="w-5 h-5" />
                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200`}>
              <LogOut className="w-5 h-5" />
              {!sidebarCollapsed && <span className="font-medium">Logout</span>}
            </button>
          </nav>

          {/* Theme Toggle */}
          <div className="absolute bottom-6 left-6">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center cursor-pointer hover:bg-white/30 transition-colors">
              <span className="text-white text-sm font-bold">N</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-4xl font-bold text-gray-800 flex items-center gap-3">
                Timeline
                <Sparkles className="w-8 h-8 text-yellow-500" />
              </h1>
              <p className="text-gray-600 mt-1">Your life journey captured in beautiful moments</p>
            </div>
            <div className="flex items-center gap-4">
              {/* View Mode Toggle */}
              <div className="bg-white rounded-2xl shadow-lg p-1.5 flex">
                <button
                  onClick={() => setViewMode('timeline')}
                  className={`px-5 py-2.5 rounded-xl text-sm font-medium transition-all ${
                    viewMode === 'timeline' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Timeline
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-5 py-2.5 rounded-xl text-sm font-medium transition-all ${
                    viewMode === 'grid' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Gallery
                </button>
                <button
                  onClick={() => setViewMode('story')}
                  className={`px-5 py-2.5 rounded-xl text-sm font-medium transition-all ${
                    viewMode === 'story' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  Stories
                </button>
              </div>
            </div>
          </div>

          {/* Quick Add Section */}
          <div className="bg-white rounded-3xl shadow-xl p-8 mb-8 border border-blue-100">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">Create Your Memory</h2>
              <div className="flex items-center gap-2">
                {['text', 'image', 'video', 'audio'].map((type) => (
                  <button
                    key={type}
                    onClick={() => setNewEvent({ ...newEvent, type })}
                    className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all ${
                      newEvent.type === type
                        ? getContentColor(type)
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                    title={`Add ${type}`}
                  >
                    {type === 'text' && <FileText className="w-5 h-5" />}
                    {type === 'image' && <Camera className="w-5 h-5" />}
                    {type === 'video' && <Video className="w-5 h-5" />}
                    {type === 'audio' && <Mic className="w-5 h-5" />}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="grid lg:grid-cols-4 gap-4">
              <input
                type="text"
                placeholder="Heading (e.g., Career Milestone)"
                className="px-5 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
              />
              <input
                type="text"
                placeholder="Title (e.g., Got Promoted!)"
                className="px-5 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
              />
              <input
                type="text"
                placeholder="Short description..."
                className="px-5 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
              />
              <div className="flex gap-2">
                <select className="flex-1 px-5 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all">
                  <option>2025</option>
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="px-8 py-3.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all shadow-lg hover:shadow-xl flex items-center gap-2"
                >
                  <Plus className="w-5 h-5" />
                  Add
                </button>
              </div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-3 mb-8 overflow-x-auto pb-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2.5 rounded-full font-medium transition-all whitespace-nowrap ${
                  selectedCategory === category
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md'
                    : 'bg-white text-gray-600 hover:text-gray-800 shadow-sm'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>

          {/* Timeline View */}
          {viewMode === 'timeline' && (
            <div className="relative">
              {/* Year Navigation */}
              <div className="bg-white rounded-3xl shadow-xl p-8 mb-12">
                <div className="relative">
                  {/* Timeline Track */}
                  <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-200 via-blue-300 to-blue-400 transform -translate-y-1/2"></div>
                  
                  {/* Year Nodes */}
                  <div className="relative flex justify-between">
                    {years.map((year, index) => {
                      const hasEvents = timelineEvents.some(e => e.year === year);
                      return (
                        <button
                          key={year}
                          onClick={() => setSelectedYear(year)}
                          className="relative group"
                        >
                          {/* Connection Line */}
                          {index < years.length - 1 && (
                            <div className="absolute top-1/2 left-full w-full h-0.5 bg-blue-300 transform -translate-y-1/2"></div>
                          )}
                          
                          {/* Year Node */}
                          <div className={`relative z-10 w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 ${
                            selectedYear === year
                              ? 'bg-gradient-to-br from-blue-500 to-blue-700 text-white shadow-2xl scale-110'
                              : hasEvents
                              ? 'bg-white border-4 border-blue-400 text-gray-800 hover:scale-105'
                              : 'bg-gray-100 border-2 border-gray-300 text-gray-400'
                          }`}>
                            <span className="font-bold text-lg">{year}</span>
                          </div>
                          
                          {/* Event Count Badge */}
                          {hasEvents && (
                            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                              {getEventsByYear(year).length}
                            </div>
                          )}
                          
                          {/* Hover Label */}
                          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <span className="text-xs text-gray-600 whitespace-nowrap">
                              {getEventsByYear(year).length} events
                            </span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Timeline Events */}
              <div className="space-y-8">
                {getEventsByYear(selectedYear).map((event, index) => (
                  <div key={event.id} className={`flex items-start gap-8 ${index % 2 === 0 ? '' : 'flex-row-reverse'}`}>
                    {/* Event Card */}
                    <div className={`flex-1 ${index % 2 === 0 ? 'text-right' : 'text-left'}`}>
                      <div className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                        {/* Media Content */}
                        {event.type === 'image' && event.thumbnail && (
                          <div className="h-64 overflow-hidden">
                            <img 
                              src={event.thumbnail} 
                              alt={event.title} 
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                          </div>
                        )}
                        {event.type === 'video' && (
                          <div className="h-64 relative overflow-hidden bg-black">
                            <img 
                              src={event.thumbnail} 
                              alt={event.title} 
                              className="w-full h-full object-cover opacity-80"
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                                <Play className="w-10 h-10 text-gray-800 ml-1" />
                              </div>
                            </div>
                            <span className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-lg text-sm">
                              {event.duration}
                            </span>
                          </div>
                        )}
                        {event.type === 'audio' && (
                          <div className={`h-32 bg-gradient-to-br ${getGradientByType(event.type)} p-6 flex items-center justify-between`}>
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                                <Music className="w-8 h-8 text-white" />
                              </div>
                              <div className="text-white">
                                <p className="text-sm opacity-90">Podcast Episode</p>
                                <p className="text-lg font-semibold">{event.duration}</p>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <div className="w-2 h-8 bg-white/40 rounded-full"></div>
                              <div className="w-2 h-12 bg-white/60 rounded-full"></div>
                              <div className="w-2 h-16 bg-white/80 rounded-full"></div>
                              <div className="w-2 h-10 bg-white/60 rounded-full"></div>
                              <div className="w-2 h-14 bg-white/40 rounded-full"></div>
                            </div>
                          </div>
                        )}
                        
                        {/* Content */}
                        <div className="p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-3">
                                <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${getContentColor(event.type)}`}>
                                  {getContentIcon(event.type)}
                                </div>
                                <span className="text-sm text-gray-500">{months[event.month - 1]} {event.year}</span>
                                {event.location && (
                                  <>
                                    <span className="text-gray-300">•</span>
                                    <span className="text-sm text-gray-500 flex items-center gap-1">
                                      <MapPin className="w-3 h-3" />
                                      {event.location}
                                    </span>
                                  </>
                                )}
                              </div>
                              <h3 className="text-xl font-bold text-gray-800 mb-1">{event.heading}</h3>
                              <h4 className="text-lg font-semibold text-gray-700 mb-2">{event.title}</h4>
                              <p className="text-gray-600 mb-3">{event.description}</p>
                              
                              {/* Text Content Preview */}
                              {event.type === 'text' && event.content && (
                                <p className="text-gray-500 italic line-clamp-2 mb-3">{event.content}</p>
                              )}
                              
                              {/* Tags */}
                              <div className="flex flex-wrap gap-2 mb-4">
                                {event.tags.map((tag, i) => (
                                  <span key={i} className="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium">
                                    #{tag}
                                  </span>
                                ))}
                              </div>
                            </div>
                            
                            <button className="text-gray-400 hover:text-gray-600 ml-4">
                              <MoreHorizontal className="w-5 h-5" />
                            </button>
                          </div>
                          
                          {/* Interaction Bar */}
                          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div className="flex items-center gap-6">
                              <button className="flex items-center gap-2 text-gray-500 hover:text-red-500 transition-colors">
                                <Heart className="w-5 h-5" />
                                <span className="text-sm font-medium">{event.likes}</span>
                              </button>
                              <button className="flex items-center gap-2 text-gray-500 hover:text-blue-500 transition-colors">
                                <MessageCircle className="w-5 h-5" />
                                <span className="text-sm font-medium">{event.comments}</span>
                              </button>
                              <button className="flex items-center gap-2 text-gray-500 hover:text-blue-500 transition-colors">
                                <Share2 className="w-5 h-5" />
                              </button>
                            </div>
                            <button className="text-gray-500 hover:text-yellow-500 transition-colors">
                              <Bookmark className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Timeline Connector */}
                    <div className="flex flex-col items-center">
                      <div className={`w-6 h-6 rounded-full border-4 border-white shadow-lg ${getContentColor(event.type)}`}></div>
                      {index < getEventsByYear(selectedYear).length - 1 && (
                        <div className="w-0.5 h-32 bg-gradient-to-b from-blue-300 to-blue-100 mt-2"></div>
                      )}
                    </div>

                    <div className="flex-1"></div>
                  </div>
                ))}
              </div>

              {/* Empty State */}
              {getEventsByYear(selectedYear).length === 0 && (
                <div className="text-center py-20 bg-white rounded-3xl shadow-lg">
                  <Clock className="w-20 h-20 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-gray-600 mb-2">No memories yet in {selectedYear}</h3>
                  <p className="text-gray-400 mb-6">Start capturing your moments</p>
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all shadow-lg"
                  >
                    Add Your First Memory
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Grid View */}
          {viewMode === 'grid' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {allEvents.map((event) => (
                <div key={event.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                  {/* Thumbnail */}
                  <div className="h-48 relative overflow-hidden bg-gray-100">
                    {event.thumbnail && (
                      <img 
                        src={event.thumbnail} 
                        alt={event.title} 
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    )}
                    {event.type === 'video' && (
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <Play className="w-12 h-12 text-white" />
                      </div>
                    )}
                    <div className={`absolute top-3 right-3 w-10 h-10 rounded-xl flex items-center justify-center ${getContentColor(event.type)} shadow-lg`}>
                      {getContentIcon(event.type)}
                    </div>
                  </div>
                  
                  <div className="p-5">
                    <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                      <span>{months[event.month - 1]} {event.year}</span>
                      {event.location && (
                        <>
                          <span>•</span>
                          <span className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {event.location}
                          </span>
                        </>
                      )}
                    </div>
                    <h3 className="font-bold text-gray-800 mb-1">{event.heading}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{event.title}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="flex items-center gap-1 text-sm text-gray-500">
                          <Heart className="w-4 h-4" />
                          {event.likes}
                        </span>
                        <span className="flex items-center gap-1 text-sm text-gray-500">
                          <MessageCircle className="w-4 h-4" />
                          {event.comments}
                        </span>
                      </div>
                      <button className="text-gray-400 hover:text-gray-600">
                        <Eye className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Story View */}
          {viewMode === 'story' && (
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-3xl shadow-xl p-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Your Life Story</h2>
                <div className="space-y-12">
                  {years.map(year => {
                    const yearEvents = getEventsByYear(year);
                    if (yearEvents.length === 0) return null;
                    
                    return (
                      <div key={year}>
                        <div className="flex items-center gap-4 mb-6">
                          <h3 className="text-2xl font-bold text-blue-600">{year}</h3>
                          <div className="flex-1 h-0.5 bg-gradient-to-r from-blue-200 to-transparent"></div>
                        </div>
                        
                        <div className="space-y-6 ml-8">
                          {yearEvents.map((event) => (
                            <div key={event.id} className="flex gap-6">
                              <div className="flex-shrink-0">
                                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getContentColor(event.type)}`}>
                                  {getContentIcon(event.type)}
                                </div>
                              </div>
                              <div className="flex-1">
                                <div className="flex items-baseline gap-3 mb-2">
                                  <h4 className="text-lg font-semibold text-gray-800">{event.title}</h4>
                                  <span className="text-sm text-gray-500">{months[event.month - 1]}</span>
                                </div>
                                <p className="text-gray-600 mb-2">{event.description}</p>
                                {event.content && event.type === 'text' && (
                                  <p className="text-gray-500 italic">{event.content}</p>
                                )}
                                <div className="flex items-center gap-4 mt-3">
                                  {event.location && (
                                    <span className="text-sm text-gray-500 flex items-center gap-1">
                                      <MapPin className="w-3 h-3" />
                                      {event.location}
                                    </span>
                                  )}
                                  <div className="flex gap-2">
                                    {event.tags.map((tag, i) => (
                                      <span key={i} className="text-xs text-blue-600">#{tag}</span>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Event Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-8 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-bold text-gray-800">Create New Memory</h3>
                  <p className="text-gray-600 mt-1">Capture this moment in your timeline</p>
                </div>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-600" />
                </button>
              </div>
            </div>
            
            <div className="p-8 space-y-6">
              {/* Content Type Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-4">What type of memory?</label>
                <div className="grid grid-cols-4 gap-4">
                  {[
                    { type: 'text', label: 'Text', icon: FileText, desc: 'Write your thoughts' },
                    { type: 'image', label: 'Photo', icon: Camera, desc: 'Upload images' },
                    { type: 'video', label: 'Video', icon: Video, desc: 'Share videos' },
                    { type: 'audio', label: 'Audio', icon: Mic, desc: 'Record sound' }
                  ].map(({ type, label, icon: Icon, desc }) => (
                    <button
                      key={type}
                      onClick={() => setNewEvent({ ...newEvent, type })}
                      className={`p-6 rounded-2xl border-2 transition-all ${
                        newEvent.type === type
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 bg-white'
                      }`}
                    >
                      <div className={`w-14 h-14 rounded-xl flex items-center justify-center mx-auto mb-3 ${
                        newEvent.type === type ? getContentColor(type) : 'bg-gray-100 text-gray-600'
                      }`}>
                        <Icon className="w-7 h-7" />
                      </div>
                      <p className="font-semibold text-gray-800">{label}</p>
                      <p className="text-xs text-gray-500 mt-1">{desc}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Date Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Year</label>
                  <select
                    value={newEvent.year}
                    onChange={(e) => setNewEvent({ ...newEvent, year: parseInt(e.target.value) })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  >
                    {years.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Month</label>
                  <select
                    value={newEvent.month}
                    onChange={(e) => setNewEvent({ ...newEvent, month: parseInt(e.target.value) })}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  >
                    {months.map((month, index) => (
                      <option key={index} value={index + 1}>{month}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Event Details */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Category Heading</label>
                  <input
                    type="text"
                    value={newEvent.heading}
                    onChange={(e) => setNewEvent({ ...newEvent, heading: e.target.value })}
                    placeholder="e.g., Career Milestone, Travel Memory, Personal Achievement"
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Event Title</label>
                  <input
                    type="text"
                    value={newEvent.title}
                    onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                    placeholder="Give your memory a title"
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Description</label>
                  <textarea
                    value={newEvent.description}
                    onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                    placeholder="Describe this moment..."
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white resize-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Location (Optional)
                  </label>
                  <input
                    type="text"
                    value={newEvent.location}
                    onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}
                    placeholder="Where did this happen?"
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Tag className="w-4 h-4 inline mr-1" />
                    Tags
                  </label>
                  <input
                    type="text"
                    placeholder="Add tags separated by commas (e.g., travel, family, achievement)"
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        // Add tag logic here
                      }
                    }}
                  />
                  <div className="flex flex-wrap gap-2 mt-2">
                    {newEvent.tags.map((tag, i) => (
                      <span key={i} className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm flex items-center gap-1">
                        #{tag}
                        <button
                          onClick={() => setNewEvent({
                            ...newEvent,
                            tags: newEvent.tags.filter((_, index) => index !== i)
                          })}
                          className="hover:text-blue-800"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Content Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">Content</label>
                {newEvent.type === 'text' && (
                  <textarea
                    value={newEvent.content}
                    onChange={(e) => setNewEvent({ ...newEvent, content: e.target.value })}
                    placeholder="Write your story here..."
                    rows={6}
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white resize-none"
                  />
                )}
                {(newEvent.type === 'image' || newEvent.type === 'video' || newEvent.type === 'audio') && (
                  <div className={`border-2 border-dashed border-gray-300 rounded-2xl p-12 text-center hover:border-gray-400 transition-colors bg-gradient-to-br ${
                    newEvent.type === 'image' ? 'from-emerald-50 to-emerald-100' :
                    newEvent.type === 'video' ? 'from-purple-50 to-purple-100' :
                    'from-orange-50 to-orange-100'
                  }`}>
                    <div className={`w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4 ${getContentColor(newEvent.type)}`}>
                      {newEvent.type === 'image' && <Camera className="w-10 h-10" />}
                      {newEvent.type === 'video' && <Video className="w-10 h-10" />}
                      {newEvent.type === 'audio' && <Mic className="w-10 h-10" />}
                    </div>
                    <p className="text-gray-700 font-medium mb-2">
                      Drag and drop your {newEvent.type} here
                    </p>
                    <p className="text-sm text-gray-500 mb-4">or</p>
                    <button className="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all shadow-lg">
                      Choose File
                    </button>
                    <p className="text-xs text-gray-500 mt-4">
                      {newEvent.type === 'image' && 'Supported: JPG, PNG, GIF (Max 10MB)'}
                      {newEvent.type === 'video' && 'Supported: MP4, MOV, AVI (Max 100MB)'}
                      {newEvent.type === 'audio' && 'Supported: MP3, WAV, M4A (Max 20MB)'}
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="p-8 border-t border-gray-100 flex gap-4">
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddEvent}
                className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all shadow-lg"
              >
                Create Memory
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimelinePage;