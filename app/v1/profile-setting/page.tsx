'use client';

import React, { useState } from 'react';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, Calendar, Camera, Mail, Phone, Tag, Info, Sparkles } from 'lucide-react';

const ProfileSettings = () => {
  const [activeSection, setActiveSection] = useState('profile');
  const [profileData, setProfileData] = useState({
    name: '',
    tagLine: '',
    aboutMe: '',
    dateOfBirth: '',
    email: '',
    phone: ''
  });
  const [profileImage, setProfileImage] = useState(null);

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const sidebarItems = [
    { id: 'profile', label: 'Profile Settings', icon: User, active: true },
    { id: 'gallery', label: 'Gallery', icon: Image },
    { id: 'slideshow', label: 'SlideShow', icon: Play },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings,
      subItems: [
        { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users },
        { id: 'password', label: 'Update Password', icon: Lock },
        { id: 'payment', label: 'Payment Settings', icon: CreditCard }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-sky-50 to-indigo-50">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-80 bg-gradient-to-b from-blue-900 to-indigo-900 h-screen sticky top-0 shadow-2xl">
          {/* Logo Section */}
          <div className="p-6 border-b border-blue-800/30 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8" viewBox="0 0 100 100">
                  <circle cx="30" cy="40" r="15" fill="white" opacity="0.9"/>
                  <circle cx="70" cy="40" r="15" fill="white" opacity="0.9"/>
                  <path d="M 30 70 Q 50 85 70 70" stroke="white" strokeWidth="3" fill="none" opacity="0.9"/>
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-semibold text-white">My Life</h1>
                <p className="text-sm text-blue-200">My Happiness</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="p-4">
            <p className="text-xs font-semibold text-blue-300 uppercase tracking-wider mb-4 px-4">GENERAL</p>
            <div className="space-y-1">
              {sidebarItems.map((item) => (
                <div key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeSection === item.id
                        ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]'
                        : 'text-blue-200 hover:bg-blue-800/30 hover:text-white'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                    {item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                  </button>
                  {item.subItems && activeSection === item.id && (
                    <div className="ml-4 mt-1 space-y-1">
                      {item.subItems.map((subItem) => (
                        <button
                          key={subItem.id}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-blue-300 hover:text-white hover:bg-blue-700/30 rounded-lg transition-all duration-200"
                        >
                          <subItem.icon className="w-4 h-4" />
                          <span>{subItem.label}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Logout Button */}
            <button className="w-full flex items-center space-x-3 px-4 py-3 mt-8 text-blue-200 hover:text-white hover:bg-red-600/20 rounded-xl transition-all duration-200 group">
              <LogOut className="w-5 h-5 group-hover:text-red-400" />
              <span className="font-medium">Logout</span>
            </button>
          </nav>

          {/* Theme Toggle */}
          <div className="absolute bottom-6 left-6">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-110">
              <span className="text-white text-sm font-bold">N</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {/* Header with User Info */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                Profile Settings
              </h1>
              <p className="text-gray-600 mt-1">Manage your personal information</p>
            </div>
            <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-5 py-2.5 rounded-2xl shadow-lg border border-blue-100">
              <div className="relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">TU</span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <p className="font-semibold text-gray-800">TEST USER</p>
                <p className="text-xs text-blue-600 font-medium">legacy creator</p>
              </div>
            </div>
          </div>

          {/* Profile Settings Content */}
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl p-8 max-w-4xl border border-blue-100">
            <div className="mb-8 flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center gap-2">
                  Enter User Details
                  <Sparkles className="w-6 h-6 text-blue-500" />
                </h2>
                <p className="text-gray-600">Keep your profile up to date to help others know you better</p>
              </div>
              <div className="bg-gradient-to-r from-blue-100 to-indigo-100 px-4 py-2 rounded-full">
                <span className="text-sm font-medium text-blue-700">Profile Strength: 75%</span>
              </div>
            </div>

            {/* Profile Picture Section */}
            <div className="mb-10 pb-10 border-b border-blue-100">
              <div className="flex items-center space-x-6">
                <div className="relative group">
                  <div className="w-28 h-28 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-3xl flex items-center justify-center border-2 border-blue-200 group-hover:border-blue-400 transition-all duration-300">
                    {profileImage ? (
                      <img src={profileImage} alt="Profile" className="w-full h-full object-cover rounded-3xl" />
                    ) : (
                      <User className="w-14 h-14 text-blue-400" />
                    )}
                    <div className="absolute inset-0 bg-black/50 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <Camera className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <button className="absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center hover:shadow-xl transition-all duration-300 hover:scale-110">
                    <Camera className="w-5 h-5 text-white" />
                  </button>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">Profile Picture</h3>
                  <p className="text-sm text-gray-600 mb-3">Upload a photo that represents you</p>
                  <div className="flex space-x-3">
                    <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300 hover:scale-105">
                      Upload Photo
                    </button>
                    <button className="px-4 py-2 text-blue-600 border border-blue-300 rounded-lg font-medium hover:bg-blue-50 transition-all duration-200">
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Name Field */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                    <User className="w-4 h-4 text-blue-500" />
                    <span>Name</span>
                  </label>
                  <input
                    type="text"
                    value={profileData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter your Name"
                    className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 placeholder-gray-400"
                  />
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                    <Mail className="w-4 h-4 text-blue-500" />
                    <span>Email Address</span>
                  </label>
                  <input
                    type="email"
                    value={profileData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter your Email"
                    className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 placeholder-gray-400"
                  />
                </div>
              </div>

              {/* Tag Line */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                  <Tag className="w-4 h-4 text-blue-500" />
                  <span>Tag Line</span>
                </label>
                <input
                  type="text"
                  value={profileData.tagLine}
                  onChange={(e) => handleInputChange('tagLine', e.target.value)}
                  placeholder="Enter your Tag Line"
                  className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 placeholder-gray-400"
                />
              </div>

              {/* About Me */}
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                  <Info className="w-4 h-4 text-blue-500" />
                  <span>About Me</span>
                </label>
                <textarea
                  value={profileData.aboutMe}
                  onChange={(e) => handleInputChange('aboutMe', e.target.value)}
                  placeholder="Tell us about yourself..."
                  rows={4}
                  className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 resize-none placeholder-gray-400"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Date of Birth */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-blue-500" />
                    <span>Date of Birth</span>
                  </label>
                  <input
                    type="date"
                    value={profileData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 text-gray-600"
                  />
                </div>

                {/* Phone Number */}
                <div className="space-y-2">
                  <label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-blue-500" />
                    <span>Phone Number</span>
                  </label>
                  <input
                    type="tel"
                    value={profileData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter your Phone Number"
                    className="w-full px-4 py-3 bg-blue-50/50 border border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:bg-white transition-all duration-200 placeholder-gray-400"
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 mt-10 pt-6 border-t border-blue-100">
              <button className="px-6 py-2.5 text-gray-600 bg-white border border-gray-300 rounded-xl font-medium hover:bg-gray-50 transition-all duration-200">
                Cancel
              </button>
              <button className="px-8 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 hover:scale-105">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;