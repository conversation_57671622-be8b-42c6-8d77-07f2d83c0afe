import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface WillByWillState {
  userinfo: any | null;
  role: string | null;
}

const initialState: WillByWillState = {
  userinfo: null,
  role: null
};

export const lifeReducer = createSlice({
  name: "life",
  initialState,
  reducers: {

    // user info
    setUserInfo: (state, action: PayloadAction<any>) => {
      state.userinfo = action.payload;
    },

    // role
    setRole: (state, action: PayloadAction<string>) => {
      state.role = action.payload;
    }
  },
});

export const { setUserInfo, setRole } = lifeReducer.actions;

export default lifeReducer.reducer;
