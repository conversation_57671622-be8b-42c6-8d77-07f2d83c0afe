import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: any = {
  registorInfo: null,
  
};

export const lifeStateReducer = createSlice({
    name: "lifeState",
    initialState,
    reducers: {
      setRegistorInfo: (state, action: PayloadAction<any>) => {
        state.registorInfo = action.payload;
      },
     
    },
  });
  
  export const { setRegistorInfo } = lifeStateReducer.actions;
  export default lifeStateReducer.reducer;
