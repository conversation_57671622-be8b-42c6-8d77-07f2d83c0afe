'use client';

import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import {
    Image,
    ImageUp,
    LucideAudioLines,
    Play,
    Type,
    X,
} from "lucide-react";
import InputWithLabel from "../ui/InputWithLabel";
import { ALLOWED_FILE_TYPES } from "@/app/services/constant";
import { DialogActions } from "@mui/material";
import Button from "../ui/Button";

interface SelectContentDialogProps {
    open: boolean;
    handleClose: () => void;
    selectedContenetType: string;
    setSelectedContentType: (
        type: "image" | "video" | "text" | "audio" | ""
    ) => void;
    selectedFile: File | null;
    setSelectedFile: (file: File | null) => void;
    setContentData: (data: any) => void;
    handleAddMedia: () => void;
    isLoading?: boolean; // Add loading prop
    fileInputRef: React.RefObject<HTMLInputElement>;
}

const contentTypes = [
    { id: "image", label: "Images", icon: Image },
    { id: "video", label: "Videos", icon: Play },
    { id: "audio", label: "audio", icon: LucideAudioLines },
    { id: "text", label: "text", icon: Type },
];

const SelectContentDialog = ({
    open,
    handleClose,
    selectedContenetType,
    setSelectedContentType,
    selectedFile,
    setSelectedFile,
    setContentData,
    handleAddMedia,
    fileInputRef,
    isLoading = false, // Default to false
}: SelectContentDialogProps) => {
    const [isDragging, setIsDragging] = React.useState(false);

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);
        const file = e.dataTransfer.files?.[0];
        if (file) {
            setSelectedFile(file);
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        console.log("out");
        if (file) {
            setSelectedFile(file);
            console.log("in");
            setContentData((prev: any) => ({
                ...prev,
                file: file,
            }));
        }
    };

    const handleSelectContentType = (id: any) => {
        setSelectedContentType(id);
        setSelectedFile(null);
        setContentData((prev: any) => ({
            ...prev,
            contentType: id,
        }));
    }

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            aria-labelledby="alert-dialog-title"
            PaperProps={{
                sx: {
                    borderRadius: "20px",
                    padding: "20px",
                    width: "100%",
                    maxWidth: "800px"
                },
            }}
        >
            <DialogTitle id="alert-dialog-title">
                <div className="text-3xl font-bold text-center text-[20px] md:text-[25px] mb-8">
                    Add Content
                    <X
                        className="absolute right-3 top-3 hover:text-gray-600 cursor-pointer"
                        onClick={handleClose}
                    />
                </div>
            </DialogTitle>
            <DialogContent>
                <div className="w-full flex flex-col gap-4 custom-scrollbar">
                    <InputWithLabel
                        id="title"
                        label="Title"
                        placeholder="Enter title"
                        type="text"
                        className="w-full text-black"
                        onChange={(e) => setContentData((prev: any) => ({
                            ...prev,
                            title: e.target.value,
                        }))}
                    />
                    <InputWithLabel
                        id="description"
                        label="Description"
                        placeholder="Enter description"
                        type="text"
                        className="text-black"
                        onChange={(e) => setContentData((prev: any) => ({
                            ...prev,
                            description: e.target.value,
                        }))}
                    />

                    <div>
                        <span className="block font-semibold text-base mb-2">
                            Select Content Type
                        </span>
                        <div className="grid grid-cols-4 gap-2">
                            {contentTypes.map(({ id, label, icon: Icon }) => (
                                <button
                                    key={id}
                                    onClick={() => handleSelectContentType(id as any)}
                                    className={`flex flex-col items-center p-4 cursor-pointer rounded-xl transition-all duration-200
                                    ${selectedContenetType === id
                                            ? "bg-blue-50 border-2 border-custom-primary shadow-sm"
                                            : "border-2 border-gray-300 hover:border-custom-primary hover:bg-gray-100"
                                        }`}
                                    aria-selected={selectedContenetType === id}
                                    role="option"
                                >
                                    <Icon
                                        className={`w-6 h-6 mb-2 ${selectedContenetType === id
                                            ? "text-custom-primary"
                                            : "text-gray-600"
                                            }`}
                                    />
                                    <span
                                        className={`text-sm font-medium ${selectedContenetType === id
                                            ? "text-custom-primary"
                                            : "text-gray-700"
                                            }`}
                                    >
                                        {label}
                                    </span>
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* File Viewer */}
                    {selectedFile && (

                        <div className="relative z-0">
                            <button
                                onClick={() => { setSelectedFile(null); console.log("ssad") }}
                                className="absolute flex justify-center z-40 items-center border-[1px] border-white right-4 w-fit bg-black/50 px-5 p-2 rounded-3xl  text-white top-8  cursor-pointer"
                            >   Clear
                                <X className="w-6 h-6" color="white" />
                            </button>
                            {selectedContenetType === "image" ? (
                                <div className="flex flex-col gap-2 mt-4">
                                    <img
                                        src={URL.createObjectURL(selectedFile)}
                                        alt="Preview"
                                        className="w-full h-96 object-cover rounded-2xl"
                                    />
                                </div>
                            ) : selectedContenetType === "video" ? (
                                <div className="flex flex-col gap-2 mt-4">
                                    <video
                                        src={URL.createObjectURL(selectedFile)}
                                        controls
                                        autoPlay
                                        className="w-full h-96 object-fit rounded-2xl"
                                    />
                                </div>
                            ) : selectedContenetType === "audio" ? (
                                <div className="flex flex-col gap-2 mt-4">
                                    <audio
                                        src={URL.createObjectURL(selectedFile)}
                                        controls
                                        className="w-full"
                                    />
                                </div>
                            ) : null

                            }
                        </div>

                    )}

                    {/* Upload Box */}

                    {!selectedFile && selectedContenetType === "image" || !selectedFile && selectedContenetType === "video" || !selectedFile && selectedContenetType === "audio" ? (
                        <div>
                            <label
                                htmlFor="image"
                                className="block font-semibold text-base mb-2"
                            >
                                {selectedContenetType}
                            </label>

                            <div
                                className={`h-[250px] border border-dashed rounded-2xl flex flex-col justify-center items-center transition-colors duration-200 ${isDragging
                                    ? "border-custom-primary bg-blue-100"
                                    : "border-gray-400"
                                    }`}
                                onClick={() => fileInputRef.current?.click()}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onDrop={handleDrop}
                            >
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    onChange={handleFileChange}
                                    accept={
                                        selectedContenetType === 'image'
                                            ? ALLOWED_FILE_TYPES.image.join(', ')
                                            : selectedContenetType === 'video'
                                                ? ALLOWED_FILE_TYPES.video.join(', ')
                                                : ALLOWED_FILE_TYPES.audio.join(', ')
                                    }
                                    className="hidden"
                                />
                                <div className={`flex flex-col bg-custom-primary/90 p-3 rounded-2xl items-center justify-center gap-2 `}>
                                    <ImageUp className="w-8 h-8 text-white" />
                                    <div className="text-center upload-box-text text-white">
                                        <span className="text-white cursor-pointer">Click here</span> to
                                        upload or drop video here
                                    </div>

                                    {/* {error && <div className="text-red-500 text-sm mt-2">{error}</div>} */}
                                </div>
                                {/* Supported formats */}
                                <div className="text-sm text-center mt-4 text-gray-500">
                                    Supported formats: {selectedContenetType === 'image'
                                        ? ALLOWED_FILE_TYPES.image.map(type => type.split('/')[1].toUpperCase()).join(', ')
                                        : selectedContenetType === 'video'
                                            ? ALLOWED_FILE_TYPES.video.map(type => type.split('/')[1].toUpperCase()).join(', ')
                                            : ALLOWED_FILE_TYPES.audio.map(type => type.split('/')[1].toUpperCase()).join(', ')}
                                </div>
                            </div>
                        </div>
                    ) : null}


                    {selectedContenetType === "text" && (
                        <div>
                            <label
                                htmlFor="text"
                                className="block font-semibold text-base mb-2"
                            >
                                text
                            </label>
                            <textarea
                                id="text"
                                placeholder="Enter your text here"
                                rows={4}
                                className="w-full h-32 p-2 border border-gray-300 rounded-md resize-none"
                                onChange={(e) => setContentData((prev: any) => ({
                                    ...prev,
                                    textContent: e.target.value,
                                }))}
                            />
                        </div>
                    )}


                </div>
            </DialogContent>
            <DialogActions>
                <Button 
                    onClick={handleClose} 
                    className="w-fit !px-14" 
                    variant="outline"
                    disabled={isLoading}
                >
                    Cancel
                </Button>
                <Button 
                    onClick={() => {
                        handleAddMedia();
                    }} 
                    className="w-fit !px-14"
                    isLoading={isLoading} // Use the loading state
                    disabled={isLoading}
                >
                    Save
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default SelectContentDialog;
