import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import { X } from "lucide-react";
import Button from "../ui/Button";
import InputWithLabel from "../ui/InputWithLabel";

interface AddPropertyDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (propertyData: PropertyFormData) => void;
  initialData?: PropertyFormData;
}

interface PropertyFormData {
  name: string;
  type: string;
  notes: string;
  hasMortgage?: string;
  ownership?: string;
  walletAddress?: string;
}

interface FormErrors {
  name: string;
  type: string;
  notes: string;
}

const AddPropertyDialog: React.FC<AddPropertyDialogProps> = ({
  open,
  onClose,
  onSave,
  initialData,
}) => {
  const [propertyFormData, setPropertyFormData] = useState<PropertyFormData>({
    name: "",
    type: "",
    notes: "",
  });

  const [errors, setErrors] = useState<FormErrors>({
    name: "",
    type: "",
    notes: "",
  });

  // Update form data when initialData changes (for editing)
  useEffect(() => {
    if (initialData) {
      setPropertyFormData(initialData);
    } else {
      // Reset form when adding new property
      setPropertyFormData({
        name: "",
        type: "",
        notes: "",
      });
    }
  }, [initialData, open]);

  const assetTypes = [
    { value: "property", label: "Property" },
    { value: "bank", label: "Bank Account" },
    { value: "insurance", label: "Life Insurance" },
    { value: "otherInsurance", label: "Other Insurance" },
    { value: "investments", label: "Investments" },
    { value: "stocks", label: "Stocks and Shares" },
    { value: "crypto", label: "Digital Assets (Crypto)" },
    { value: "otherAssets", label: "Other Assets (e.g. Jewellery, Car, Art, Watches)" },
    { value: "moneyToReceive", label: "Money to Receive from Friends or Family" },
    { value: "other", label: "Other" },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setPropertyFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
    // Clear error when user types
    if (errors[id as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [id]: "",
      }));
    }
  };

  const handleSelectChange = (e: any) => {
    const { value } = e.target;
    
    // Clear all input fields when asset type changes
    setPropertyFormData((prev) => ({
      ...prev,
      type: value,
      name: "",
      notes: "",
      hasMortgage: undefined,
      ownership: undefined,
      walletAddress: undefined,
    }));
    
    // Clear errors when asset type changes
    setErrors((prev) => ({
      ...prev,
      type: "",
      name: "",
      notes: "",
    }));
  };

  const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    if (!value.trim() && id !== "notes" && id !== "walletAddress") {
      // Notes and wallet address are optional
      setErrors((prev) => ({
        ...prev,
        [id]: `${id.charAt(0).toUpperCase() + id.slice(1)} is required`,
      }));
    }
  };

  const handleClose = () => {
    // Reset errors when closing
    setErrors({
      name: "",
      type: "",
      notes: "",
    });
    onClose();
  };

  const handleMortgageSelection = (value: string) => {
    setPropertyFormData((prev) => ({
      ...prev,
      hasMortgage: value,
    }));
  };

  const handleOwnershipSelection = (value: string) => {
    setPropertyFormData((prev) => ({
      ...prev,
      ownership: value,
    }));
  };

  const handleSubmitForm = () => {
    // Basic validation
    const newErrors = {
      name: !propertyFormData.name.trim() ? "Name is required" : "",
      type: !propertyFormData.type.trim() ? "Type is required" : "",
      notes: "",
    };

    setErrors(newErrors);

    // Check if there are any errors
    if (Object.values(newErrors).some((error) => error !== "")) {
      return;
    }

    // For property type, check if mortgage and ownership are selected
    if (propertyFormData.type === "property") {
      if (!propertyFormData.hasMortgage) {
        alert("Please select if the property has a mortgage");
        return;
      }
      if (!propertyFormData.ownership) {
        alert("Please select who owns the property");
        return;
      }
    }

    // Call the onSave callback with the form data
    onSave(propertyFormData);

    // Close dialog
    handleClose();
  };

  // Render different input fields based on asset type
  const renderAssetSpecificFields = () => {
    switch (propertyFormData.type) {
      case "bank":
        return (
          <InputWithLabel
            id="name"
            label="Bank Name"
            placeholder="e.g. HSBC, Nationwide"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "insurance":
        return (
          <InputWithLabel
            id="name"
            label="Provider"
            placeholder="e.g. Legal & General"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "otherInsurance":
        return (
          <InputWithLabel
            id="name"
            label="Provider"
            placeholder="e.g. Aviva, AXA"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "investments":
        return (
          <InputWithLabel
            id="name"
            label="Investment Name"
            placeholder="e.g. ISA, SIPP"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "stocks":
        return (
          <InputWithLabel
            id="name"
            label="Company Name"
            placeholder="e.g. Apple, Amazon"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "crypto":
        return (
          <>
            <InputWithLabel
              id="name"
              label="Crypto Currency"
              placeholder="e.g. Bitcoin, Ethereum"
              type="text"
              value={propertyFormData.name}
              onChange={handleInputChange}
              onBlur={handleBlur}
              error={errors.name}
            />
            <InputWithLabel
              id="walletAddress"
              label="Wallet Address (Optional)"
              placeholder="e.g. 0x1234..."
              type="text"
              value={propertyFormData.walletAddress || ""}
              onChange={handleInputChange}
              error=""
            />
          </>
        );
      case "otherAssets":
        return (
          <InputWithLabel
            id="name"
            label="Asset Description"
            placeholder="e.g. Rolex Watch, Diamond Ring"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "moneyToReceive":
        return (
          <InputWithLabel
            id="name"
            label="Debtor Name"
            placeholder="e.g. John Smith"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
      case "property":
        return (
          <>
            <InputWithLabel
              id="name"
              label="Property Address"
              placeholder="e.g. 123 Main Street"
              type="text"
              value={propertyFormData.name}
              onChange={handleInputChange}
              onBlur={handleBlur}
              error={errors.name}
            />
            <div className="mt-4">
              <p className="text-left text-[15px] font-semibold mb-2">
                Does this property have a mortgage?
              </p>
              <div className="flex gap-4">
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full hover:bg-gray-100 ${
                    propertyFormData.hasMortgage === "yes" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleMortgageSelection("yes")}
                >
                  Yes
                </button>
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full hover:bg-gray-100 ${
                    propertyFormData.hasMortgage === "no" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleMortgageSelection("no")}
                >
                  No
                </button>
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full hover:bg-gray-100 ${
                    propertyFormData.hasMortgage === "unknown" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleMortgageSelection("unknown")}
                >
                  I don&apos;t know
                </button>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-left text-[15px] font-semibold mb-2">
                Who owns the property?
              </p>
              <div className="flex flex-col gap-2">
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full text-left hover:bg-gray-100 ${
                    propertyFormData.ownership === "sole" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleOwnershipSelection("sole")}
                >
                  I am the only owner
                </button>
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full text-left hover:bg-gray-100 ${
                    propertyFormData.ownership === "joint" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleOwnershipSelection("joint")}
                >
                  I own it jointly with someone else
                </button>
                <button
                  type="button"
                  className={`py-2 px-4 border rounded-full text-left hover:bg-gray-100 ${
                    propertyFormData.ownership === "unknown" ? "bg-blue-100 border-blue-500" : ""
                  }`}
                  onClick={() => handleOwnershipSelection("unknown")}
                >
                  I don&apos;t know
                </button>
              </div>
            </div>
          </>
        );
      default:
        return (
          <InputWithLabel
            id="name"
            label="Account/Property Name"
            placeholder="e.g. Main Residence, Savings Account"
            type="text"
            value={propertyFormData.name}
            onChange={handleInputChange}
            onBlur={handleBlur}
            error={errors.name}
          />
        );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle id="alert-dialog-title">
        <div className="relative text-center font-conthrax text-[26px] md:text-[34px] my-4">
          <div>{initialData ? "Edit Property" : "Add Account or Property"}</div>
          <X
            className="absolute right-0 top-0 hover:text-gray-500 cursor-pointer"
            onClick={handleClose}
          />
        </div>
      </DialogTitle>
      <DialogContent>
        <div className="flex flex-col gap-4 p-1 w-full">
          {/* Asset Type Select */}
          <FormControl fullWidth className="mt-4">
            <InputLabel id="asset-type-label">Asset Type</InputLabel>
            <Select
              labelId="asset-type-label"
              id="asset-type"
              value={propertyFormData.type}
              label="Asset Type"
              onChange={handleSelectChange}
              className="rounded-full"
              sx={{ height: "50px", borderRadius: '25px'}}
            >
              {assetTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
            {errors.type && (
              <p className="text-red-500 text-sm mt-1">{errors.type}</p>
            )}
          </FormControl>

          {/* Render different fields based on asset type */}
          {propertyFormData.type && (
            <div className="mt-4">
              {renderAssetSpecificFields()}
            </div>
          )}

          {/* Common fields for all asset types */}
          {propertyFormData.type && (
            <>
              <InputWithLabel
                id="notes"
                label="Additional Notes (Optional)"
                placeholder="Any additional information"
                type="text"
                value={propertyFormData.notes}
                onChange={handleInputChange}
                error={errors.notes}
              />
            </>
          )}

          <div className="text-left font-medium text-black text-[16px] mt-1">
            We&apos;ll include this information in your will to help your executors
            identify your assets. We will never contact these providers without
            your permission.
          </div>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleSubmitForm} className="m-2">
          {initialData ? "Update Property" : "Save Property"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddPropertyDialog;
