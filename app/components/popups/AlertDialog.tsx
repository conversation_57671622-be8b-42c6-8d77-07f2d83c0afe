import React from 'react'
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '../ui/Button';

interface AlertDialogProps {
    open: boolean;
    handleClose: () => void;
    handleConfirm: () => void | any;
    confirmText: string;
    cancelText: string;
    title: string;
    description: string;
}

const AlertDialog = ({ open, handleClose, handleConfirm, confirmText, cancelText, title, description }: AlertDialogProps) => {
    return (
        <div>
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                PaperProps={{
                    sx: {
                        borderRadius: '20px',
                        padding: '20px',
                    },
                }}
            >
                <DialogTitle id="alert-dialog-title ">
                    <div className='font-conthrax text-center text-[20px] md:text-[25px] mt-4'>
                        {title}
                    </div>
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        <span className="block text-center w-full font-medium">
                            {description}
                        </span>
                    </DialogContentText>
                </DialogContent>
                <DialogActions >
                    <Button variant='outline' className='text-custom-blue' onClick={handleClose}>{cancelText}</Button>
                    <Button onClick={handleConfirm} >{confirmText}</Button>
                </DialogActions>

            </Dialog>
        </div>
    )
}

export default AlertDialog