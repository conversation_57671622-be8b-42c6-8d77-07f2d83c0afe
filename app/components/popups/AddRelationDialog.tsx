import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { X } from 'lucide-react';
import React from 'react'
import InputWithLabel from '../ui/InputWithLabel';
import { relationshipSuggestions } from '@/app/config/constant';
import { z } from 'zod';
import { toast } from 'sonner';

// Define schema for form validation
const personSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Please enter a valid email address").optional().or(z.literal("")),
  age: z.string().optional().refine((val) => !val || !isNaN(Number(val)), {
    message: "Age must be a number",
  }),
  relationship: z.string().min(1, "Relationship is required"),
});

type FormErrors = {
  name?: string;
  email?: string;
  age?: string;
  relationship?: string;
};

const AddRelationDialog = ({ open, handleClose, handleSubmitForm, userId, appServiceInstance }: any) => {

  const [personFormData, setPersonFormData] = React.useState({
    name: "",
    email: "",
    age: "",
    relationship: "",
  });

  const [errors, setErrors] = React.useState({
    name: "",
    email: "",
    age: "",
    relationship: "",
  });

  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setPersonFormData((prev) => ({
      ...prev,
      [id]: value,
    }));

    if (errors[id as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [id]: undefined,
      }));
    }
  };

  const handleBlur = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    if (id === "name" || id === "relationship") {
      if (!value.trim()) {
        setErrors((prev) => ({
          ...prev,
          [id]: `${id.charAt(0).toUpperCase() + id.slice(1)} is required`,
        }));
      }
    }
  };

  const handleSubmit = async () => {
    try {
      const validatedData = personSchema.parse(personFormData);
      const payload = {
        userId,
        name: validatedData.name,
        email: validatedData.email || "",
        age: validatedData.age ? Number(validatedData.age) : undefined,
        relationship: validatedData.relationship,
      };

      const response = await appServiceInstance.addRelatedPerson(payload);

      if (response?.status === 201) {
        setPersonFormData({
          name: "",
          email: "",
          age: "",
          relationship: "",
        });
        toast.success("Person added successfully!");
        handleClose();
        handleSubmitForm(validatedData);
      } else {
        toast.error(response?.data?.message || "Failed to add person");
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.errors.forEach((err) => {
          const path = err.path[0];
          if (path) {
            newErrors[path as keyof FormErrors] = err.message;
          }
        });
        setErrors(newErrors);
        toast.error("Please check the form for errors");
      } else {
        console.error("Error adding person:", error);
        toast.error("An error occurred while adding the person");
      }
    }
  };

  return (
    <div> 
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle id="alert-dialog-title">
          <div className="relative text-center font-conthrax text-[26px] md:text-[34px] my-4">
            <div>Add Person</div>
            <X
              className="absolute right-0 top-0 hover:text-gray-500 cursor-pointer"
              onClick={handleClose}
            />
          </div>
        </DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-2 p-1 w-full">
            <InputWithLabel
              id="name"
              label="Preferred name"
              placeholder="John Doe"
              type="text"
              value={personFormData.name}
              onBlur={handleBlur}
              onChange={handleInputChange}
              error={errors.name}
            />
            <InputWithLabel
              id="email"
              label="Email (optional)"
              placeholder="<EMAIL>"
              type="text"
              value={personFormData.email}
              onChange={handleInputChange}
              onBlur={handleBlur}
              error={errors.email}
            />
            <div className="text-left font-medium text-black text-[16px] mt-1">
              We’ll use this to help whoever deals with your will identify this person. We will never contact them without your permission.
            </div>
            <InputWithLabel
              id="age"
              label="Age (optional)"
              placeholder="45"
              type="text"
              value={personFormData.age}
              onChange={handleInputChange}
              onBlur={handleBlur}
              error={errors.age}
            />
            <InputWithLabel
              id="relationship"
              label="Relationship"
              placeholder="e.g. Spouse, Child, Friend"
              type="text"
              value={personFormData.relationship}
              onChange={handleInputChange}
              onBlur={handleBlur}
              error={errors.relationship}
            />
            {!personFormData.relationship && (
              <div className="mt-1">
                <p className="text-left text-[14px] text-gray-600 mb-2">Common relationships:</p>
                <div className="flex flex-wrap gap-2">
                  {relationshipSuggestions.map((rel) => (
                    <button
                      key={rel}
                      type="button"
                      className="py-1 px-3 text-sm border rounded-full text-gray-700 hover:bg-gray-100"
                      onClick={() => {
                        setPersonFormData((prev) => ({
                          ...prev,
                          relationship: rel,
                        }));
                        if (errors.relationship) {
                          setErrors((prev) => ({
                            ...prev,
                            relationship: undefined,
                          }));
                        }
                      }}
                    >
                      {rel}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-center items-center w-full">
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                borderRadius: "30px",
                backgroundColor: "#00AFE5",
                color: "white",
                textTransform: "none",
                fontSize: "16px",
                fontWeight: "bold",
                padding: "10px 20px",
              }}
            >
              Save Person
            </Button>
          </div>
        </DialogActions>
      </Dialog>
    </div>
  )
}

export default AddRelationDialog
