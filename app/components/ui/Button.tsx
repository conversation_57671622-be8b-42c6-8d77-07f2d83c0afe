import React from "react";

const buttonVariants = {
  primary: "bg-blue-600 text-white hover:bg-blue-700",
  secondary: "bg-gray-100 text-gray-700 hover:bg-gray-200",
  success: "bg-green-500 text-white hover:bg-green-600",
  danger: "bg-red-500 text-white hover:bg-red-600",
  outline: "bg-white border border-custom-blue text-black",
};

const buttonSizes = {
  small: "px-3 py-1 text-sm",
  medium: "px-4 py-3 text-[15px]",
  large: "px-6 py-3 text-lg",
};

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: keyof typeof buttonVariants;
  size?: keyof typeof buttonSizes;
  icon?: React.ReactNode;
  isLoading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "medium",
  icon,
  isLoading = false,
  children,
  className = "",
  ...props
}) => {
  const combinedClasses = `
    inline-flex items-center justify-center font-medium rounded-xl transition-colors
    focus:outline-none focus:ring-2 focus:ring-offset-2
    ${buttonVariants[variant]}
    ${buttonSizes[size]}
    ${isLoading || props.disabled ? "opacity-50 cursor-not-allowed" : ""}
    ${className}
  `;

  return (
    <button className={combinedClasses.trim()} disabled={isLoading || props.disabled} {...props}>
      {isLoading && (
        <svg
          className="animate-spin mr-2 h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8v8H4z"
          ></path>
        </svg>
      )}
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

export default Button;
