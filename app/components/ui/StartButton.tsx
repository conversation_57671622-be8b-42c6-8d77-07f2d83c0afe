import Link from "next/link";
import React from "react";

interface StartButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  to?: string;
  text?: string;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

const StartButton = ({ 
  to, 
  text, 
  className, 
  disabled,
  loading = false,
  type = "button",
  ...props 
}: StartButtonProps) => {
  const arrowIcon = (
    <svg width="25" height="25" viewBox="0 0 25 25" fill="none">
      <path
        d="M12.5146 13.3301L5.87402 19.9707C5.41504 20.4297 4.67285 20.4297 4.21875 19.9707L3.11523 18.8672C2.65625 18.4082 2.65625 17.666 3.11523 17.2119L7.82227 12.5049L3.11523 7.79785C2.65625 7.33887 2.65625 6.59668 3.11523 6.14258L4.21387 5.0293C4.67285 4.57031 5.41504 4.57031 5.86914 5.0293L12.5098 11.6699C12.9736 12.1289 12.9736 12.8711 12.5146 13.3301ZM21.8896 11.6699L15.249 5.0293C14.79 4.57031 14.0479 4.57031 13.5938 5.0293L12.4902 6.13281C12.0312 6.5918 12.0312 7.33398 12.4902 7.78809L17.1973 12.4951L12.4902 17.2021C12.0312 17.6611 12.0312 18.4033 12.4902 18.8574L13.5938 19.9609C14.0527 20.4199 14.7949 20.4199 15.249 19.9609L21.8896 13.3203C22.3486 12.8711 22.3486 12.1289 21.8896 11.6699Z"
        fill="#00AFE5"
      />
    </svg>
  );

  const loadingSpinner = (
    <div className="inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
  );

  const buttonContent = (
    <>
      {loading ? (
        <div className="flex items-center gap-3">
          {loadingSpinner}
          <span className="py-2">Processing...</span>
          <span className="p-2 bg-white rounded-full">{arrowIcon}</span>
        </div>
      ) : (
        <>
          {text || 'Lets Get Started'}
          <span className="p-2 bg-white rounded-full">{arrowIcon}</span>
        </>
      )}
    </>
  );

  const commonClassName = `py-2 pr-2 pl-5 items-center mt-0 rounded-full font-bold text-white inline-flex gap-3 transition-opacity ${
    className || ''
  } ${
    loading || disabled 
      ? 'bg-[#00AFE5]/80 cursor-not-allowed' 
      : 'bg-[#00AFE5] hover:bg-[#0095cc]'
  }`;

  // If it's used within a form (type="submit") or no "to" prop is provided, render as button
  if (type === "submit" || !to) {
    return (
      <div>
        <button
          type={type}
          className={commonClassName}
          disabled={disabled || loading}
          {...props}
        >
          {buttonContent}
        </button>
      </div>
    );
  }

  // Otherwise render as Link
  return (
    <div>
      <Link
        href={to}
        className={commonClassName}
        aria-disabled={disabled || loading}
      >
        {buttonContent}
      </Link>
    </div>
  );
};

export default StartButton;