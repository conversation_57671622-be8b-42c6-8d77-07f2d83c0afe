import React from 'react';
import { Skeleton, Box } from '@mui/material';

interface OnboardingSkeletonProps {
  withHeader?: boolean;
  withImage?: boolean;
  withOptions?: boolean;
  optionsCount?: number;
  withButton?: boolean;
  className?: string;
}

const OnboardingSkeleton: React.FC<OnboardingSkeletonProps> = ({
  withHeader = true,
  withImage = true,
  withOptions = true,
  optionsCount = 4,
  withButton = true,
  className = '',
}) => {
  return (
    <Box className={`bg-white rounded-lg p-6 md:p-10 max-w-xl mx-auto flex flex-col items-center ${className}`}>
      {/* Logo skeleton */}
      {withHeader && (
        <>
          <Skeleton variant="rectangular" width={160} height={60} className="mx-auto mb-2" />
          <Skeleton variant="text" width={200} className="mx-auto mb-6" />
        </>
      )}
      
      {/* Image skeleton */}
      {withImage && (
        <Skeleton variant="circular" width={120} height={120} className="mx-auto mb-6" />
      )}
      
      {/* Title and description */}
      <Skeleton variant="rectangular" width="70%" height={32} className="mx-auto mb-3" />
      <Skeleton variant="rectangular" width="50%" height={20} className="mx-auto mb-2" />
      <Skeleton variant="rectangular" width="60%" height={20} className="mx-auto mb-8" />
      
      {/* Options */}
      {withOptions && (
        <Box className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full mb-8">
          {Array.from({ length: optionsCount }).map((_, index) => (
            <Skeleton 
              key={index} 
              variant="rectangular" 
              height={56} 
              className="rounded-full"
            />
          ))}
        </Box>
      )}
      
      {/* Button */}
      {withButton && (
        <Skeleton 
          variant="rectangular" 
          width={200} 
          height={48} 
          className="mx-auto rounded-full mt-4"
        />
      )}
    </Box>
  );
};

export default OnboardingSkeleton;