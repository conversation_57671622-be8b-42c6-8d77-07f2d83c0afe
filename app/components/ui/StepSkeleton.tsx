import React from "react";

interface StepSkeletonProps {
  hasImage?: boolean;
  hasTitle?: boolean;
  hasDescription?: boolean;
  hasOptions?: boolean;
  optionsCount?: number;
  hasButton?: boolean;
  className?: string;
}

const StepSkeleton: React.FC<StepSkeletonProps> = ({
  hasImage = true,
  hasTitle = true,
  hasDescription = true,
  hasOptions = true,
  optionsCount = 4,
  hasButton = true,
  className = "",
}) => {
  return (
    <div className={`bg-white comn-card rounded-lg py-[15px] md:p-10 p-7 md:w-[48rem] w-[85%] mx-auto max-w-[4xl] flex flex-col justify-center items-center text-center animate-pulse ${className}`}>
      {/* Logo skeleton */}
      <div className="h-8 w-32 bg-gray-200 rounded mb-4"></div>
      
      {/* Image skeleton */}
      {hasImage && (
        <div className="h-32 w-32 bg-gray-200 rounded-full mb-6"></div>
      )}
      
      {/* Title skeleton */}
      {hasTitle && (
        <div className="h-8 w-3/4 bg-gray-200 rounded mb-3"></div>
      )}
      
      {/* Description skeleton */}
      {hasDescription && (
        <>
          <div className="h-4 w-2/3 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-1/2 bg-gray-200 rounded mb-6"></div>
        </>
      )}
      
      {/* Options skeleton */}
      {hasOptions && (
        <div className="grid md:grid-cols-2 gap-4 mb-6 mt-5 w-full">
          {Array.from({ length: optionsCount }).map((_, index) => (
            <div key={index} className="h-12 bg-gray-200 rounded-full"></div>
          ))}
        </div>
      )}
      
      {/* Button skeleton */}
      {hasButton && (
        <div className="h-12 w-48 bg-gray-200 rounded-full mt-4"></div>
      )}
    </div>
  );
};

export default StepSkeleton;