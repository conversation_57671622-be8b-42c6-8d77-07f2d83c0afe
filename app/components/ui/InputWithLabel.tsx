"use client";

import React, { useState, forwardRef } from "react";
import { FaEye, FaEyeSlash } from "react-icons/fa";

interface InputProps {
  label?: string;
  type?: string;
  placeholder?: string;
  id?: string;
  name?: string;
  value?: string;
  required?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string | boolean;
  helperText?: string;
  className?: string;
  nextRef?: React.RefObject<HTMLInputElement> | null;
  readOnly?: boolean;
}

const InputWithLabel = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      type = "text",
      placeholder = "",
      id,
      name,
      value,
      required = false,
      onChange,
      onBlur,
      error,
      helperText,
      className = "",
      nextRef,
      readOnly,
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Check if Enter is pressed and nextRef is valid
      if (e.key === "Enter" && nextRef && nextRef.current) {
        e.preventDefault(); // Prevent form submission or default action
        nextRef.current.focus(); // Move focus to the next input
      }
    };

    return (
      <div className=" justify-left mb-0 form-group flex flex-col gap-3">
        {label && (
          <label className="font-semibold w-fit md:text-[15px] text-[14px]" htmlFor={id}>
            {label}
          </label>
        )}
        <div>
          <div className="relative w-full">
            <input
              className={`h-[44px] rounded-full border-gray-300 border pl-4 w-full focus:outline-black ${className} ${
                error ? "input-error border-red-400" : ""
              }`}
              id={id}
              name={name}
              placeholder={placeholder}
              type={type === "password" && showPassword ? "text" : type}
              value={value}
              onChange={onChange}
              onBlur={onBlur}
              onKeyDown={handleKeyDown}
              ref={ref}
              required={required}
              // readOnly={readOnly}
              disabled={readOnly}
            />
            {type === "password" && (
              <span
                className="absolute cursor-pointer inset-y-0 right-4 flex items-center pl-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEye /> : <FaEyeSlash />}
              </span>
            )}
          </div>
          {(helperText || error) && (
            <div
              className={`mt-1 text-left ml-2 text-sm ${
                error ? "text-red-500" : "text-gray-500"
              }`}
            >
              {helperText || (typeof error === "string" ? error : "")}
            </div>
          )}
        </div>
      </div>
    );
  }
);

InputWithLabel.displayName = "InputWithLabel";

export default InputWithLabel;
