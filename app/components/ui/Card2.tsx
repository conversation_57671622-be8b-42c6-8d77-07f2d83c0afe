import React from 'react';

const Card2 = ({ item, handleDeleteOpen, setSelectedContactId }: any) => {


    const handleClick = () => {
        // Check if item exists and has an ID property (could be _id or id)
        const contactId = item?._id || item?.id;
        
        if (contactId) {
            console.log("Setting contact ID for deletion:", contactId);
            setSelectedContactId(contactId);
            
            // Only open the dialog after setting the ID
            setTimeout(() => {
                handleDeleteOpen();
            }, 0);
        } else {
            console.error("Contact item or ID is missing:", item);
        }
    }

    return (
        <div className="card shadow-3xl">
            <button className="mail">
                <svg xmlns="http://www.w3.org/2000/svg"
                    width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                    strokeLinejoin="round" className="lucide lucide-mail">
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                </svg>
            </button>

            <button className='profile-pic'>
                <img src={'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRKZMX9vAdr317mKWVxzEw-eT-wpDqOuAZrFw&s'} alt="profile" width={100} height={100} />
            </button>

            <div className="bottom">
                <div className="content">
                    <span className="name !capitalize">{item?.name ?? ""}</span>
                    <span className="about-me">{item?.email ?? ""}</span>
                    <span className="about-me"> <span className='font-bold'>Status: </span> {(item?.status).toUpperCase() ?? ""}</span>
                </div>

                <div className="bottom-bottom ">
                    <div className="social-links-container">
                        {/* Social Icon 1 */}
                        {/* <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                            <path d="M6-582H-2a4,4,0,0,1-4-4v-8a4,4,0,0,1,4-4H6a4,4,0,0,1,4,4v8A4,4,0,0,1,6-582ZM2-594a4,4,0,0,0-4,4,4,4,0,0,0,4,4,4,4,0,0,0,4-4A4.005,4.005,0,0,0,2-594Zm4.5-2a1,1,0,0,0-1,1,1,1,0,0,0,1,1,1,1,0,0,0,1-1A1,1,0,0,0,6.5-596ZM2-587.5A2.5,2.5,0,0,1-.5-590,2.5,2.5,0,0,1,2-592.5,2.5,2.5,0,0,1,4.5-590,2.5,2.5,0,0,1,2-587.5Z" transform="translate(6 598)" />
                        </svg> */}


                        {/* Social Icon 2 */}
                        {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path d="M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z" />
                        </svg> */}

                        {/* Social Icon 3 (GitHub) */}
                        {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512">
                            <path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6..." />
                        </svg> */}
                    </div>

                    <button 
                        className="button !cursor-pointer !bg-cutom-primary !text-white" 
                        onClick={handleClick}
                        disabled={!item || (!item._id && !item.id)}
                    >
                        Delete This Contact
                    </button>
                </div>
            </div>
        </div>
    );
};

export default Card2;
