"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";

const Logo: React.FC<LogoProps> = ({
  href = "#",
  src = "/img/logo.png",
  alt = "Logo",
  width = 183,
  height = 60,
  tagline,
  className = "",
}) => {
  return (
    <Link href={href} className={`flex flex-col gap-1 ${className}`}>
      <Image src={src} alt={alt} width={width} height={height} />
      {tagline && (
        <span className="text-[10px] font-semibold text-black">{tagline}</span>
      )}
    </Link>
  );  
};

export default Logo;
