import React, { useState } from "react";
import WavesurferPlayer from "@wavesurfer/react";
import styled from "styled-components";
import WaveSurfer from "wavesurfer.js";
import { PauseCircle, PlayCircle, Volume2, VolumeX } from "lucide-react";

interface WaveformProps {
  src: string;
}

const WaveformContainer = styled.div`
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 10px;
  padding: 0.5rem 1rem;
  gap: 12px;
  max-width: 400px;
  width: 100%;
`;

const IconButton = styled.div`
  cursor: pointer;
  color: #425586;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  width: 40px;
  height: 40px;
  svg {
    stroke-width: 1.5;
  }
  &:hover {
    background: #f0f2f5;
  }
`;

const Wave = styled.div`
  flex: 1;
  justify-content: center;
  aligh-item: center
  height: 65px;
  margin: 0 0.25rem;
`;

const TimeDisplay = styled.div`
  font-size: 12px;
  font-family: monospace;
  color: #425586;
  min-width: 60px;
  text-align: right;
`;

const typeSafeSrc =
  "https://api.twilio.com/2010-04-01/Accounts/AC25aa00521bfac6d667f13fec086072df/Recordings/RE6d44bc34911342ce03d6ad290b66580c.mp3";

const Waveform: React.FC<WaveformProps> = ({ src = typeSafeSrc }) => {
  const [wavesurfer, setWavesurfer] = useState<WaveSurfer | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);

  const onReady = (ws: WaveSurfer) => {
    setWavesurfer(ws);
    setDuration(ws.getDuration());

    ws.on("audioprocess", () => {
      setCurrentTime(ws.getCurrentTime());
    });

    ws.on("seek", () => {
      setCurrentTime(ws.getCurrentTime());
    });

    ws.on("finish", () => {
      setIsPlaying(false);
    });
  };

  const onPlayPause = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    if (wavesurfer) wavesurfer.playPause();
  };

  const toggleMute = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    if (wavesurfer) {
      wavesurfer.setMuted(!isMuted);
      setIsMuted(!isMuted);
    }
  };

  const formatTime = (time: number): string => {
    const mins = Math.floor(time / 60);
    const secs = Math.floor(time % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };
console.log("first", src)
  return (
    <WaveformContainer>
      <IconButton onClick={(e) => onPlayPause(e)}>
        {!isPlaying ? <PlayCircle size={40} color="#425586" /> : <PauseCircle size={40} color="#425586" />}
      </IconButton>

      <Wave>
        <WavesurferPlayer
          height={50}
          waveColor="#d3d7e1"
          progressColor="#425586"
          cursorColor="transparent"
          barWidth={2}
          barRadius={1}
          barGap={2}
          cursorWidth={0}
          url={src}
          backend="MediaElement"
          onReady={onReady}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        />
      </Wave>

      <TimeDisplay>
        {formatTime(currentTime)} / {formatTime(duration)}
      </TimeDisplay>

      <IconButton onClick={(e) => toggleMute(e)}>
        {isMuted ? <VolumeX size={23} color="#425586" /> : <Volume2 size={23}  color="#425586" />}
      </IconButton>
    </WaveformContainer>
  );
};

export default Waveform;
