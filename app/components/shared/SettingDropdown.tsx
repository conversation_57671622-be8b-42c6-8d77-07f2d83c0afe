import { useEffect, useState } from 'react';
import { ChevronDown, ChevronUp, Settings } from 'lucide-react';
import Link from 'next/link';
import {  useAppSelector } from '@/app/store/hooks';
import { getUserInfoData } from '@/app/store/reducers/life.selector';
import { useRouter, usePathname } from 'next/navigation';

const SettingDropDown = () => {
  const [expanded, setExpanded] = useState(true);
  const pathname = usePathname();
  const userInfo = useAppSelector(getUserInfoData);
  const [isMounted, setIsMounted] = useState(false)
  const router = useRouter();

  useEffect(() => {
    if (userInfo) {
      setIsMounted(true);
    }

  }, [])

  const menuItems = [
    { name: 'Invite Trusted Contacts', url: 'trusted-contacts' },
    { name: 'Update Password', url: 'update-password' },
    { name: 'Payment Settings', url: 'payment-settings' },
  ];

  const isAdmin = userInfo?.role?.name === 'admin';

  const handleSettingsClick = () => {
    if (isAdmin) {
      router.push('/personal-settings');
    } else {
      setExpanded((prev) => !prev);
    }
  };

 if(!isMounted) return ;

  return (

    <div className="w-full">
      <div className="text-black rounded-md w-full mb-4">
        <button
          className="flex items-center font-semibold py-2.5 text-[15px] justify-between w-full px-4"
          onClick={handleSettingsClick}
        >
          <div className="flex items-center gap-4">
            <Settings color="#425586" size={23} />
            <span>Settings</span>
          </div>
          {!isAdmin && (expanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />)}
        </button>

        {!isAdmin && expanded && (
          <div className="space-y-1">
            {menuItems.map((item) => (
              <Link
                key={item.name}
                href={`/${item.url}`}
                className={`block py-2 px-10 text-[15px]    font-medium rounded-md transition ${pathname.includes(item.url)
                  ? 'text-white bg-custom-primary '
                  : 'text-black hover:bg-custom-secondary'
                  }`}
              >
                • {item.name}
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );

};

export default SettingDropDown;
