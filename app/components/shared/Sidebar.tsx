"use client"
import React, { useState, useRef, useEffect } from 'react';
import { BadgeEuro, Images, LogOutIcon, Menu, User2, UserRoundCog, X } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { Skeleton } from "@mui/material";
import appServiceInstance from '@/app/services/AppServices';
import Cookies from 'js-cookie';
import { toast } from 'sonner';
import Button from '../ui/Button';
import SettingDropDown from './SettingDropdown';
import Image from 'next/image';
import { setRole, setUserInfo } from '@/app/store/reducers/life.reducer';
import { getUserInfoData } from '@/app/store/reducers/life.selector';
import { FaTimeline } from "react-icons/fa6";

const Sidebar = () => {

  const pathname = usePathname();
  const dispatch = useAppDispatch();
  // const [active, setActive] = useState(pathname);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();
  const dropdownRef = useRef(null);
  const userInfo = useAppSelector(getUserInfoData);
  const [isMounted, setIsMounted] = useState(false)
  const logoutDialogRef = useRef(null);

  const toggleDropdown = () => {
    setShowDropdown((prev) => !prev);
  };

  const handleLogoutClick = () => {
    setShowLogoutDialog(true);
  };

  const handleLogoutConfirm = async () => {
    try {
      setIsLoggingOut(true);
      const response = await appServiceInstance.signOut();

      if (response.status === 200) {
        // Clear cookies
        Cookies.remove('token');
        Cookies.remove('expiryTime');
        Cookies.remove('role');
        dispatch(setUserInfo(null));
        dispatch(setRole(""));
        toast.success("Logged out successfully");

        // Redirect to login page
      } else {
        Cookies.remove('token');
        Cookies.remove('expiryTime');
        Cookies.remove('role');
        router.push('/login');
        toast.success("Logged out successfully");
      }
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout");
    } finally {
      setIsLoggingOut(false);
      setShowLogoutDialog(false);
    }
  };

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !(dropdownRef.current as any).contains(event.target)
      ) {
        setShowDropdown(false);
      }
      if (
        logoutDialogRef.current &&
        showLogoutDialog &&
        !(logoutDialogRef.current as any).contains(event.target)
      ) {
        setShowLogoutDialog(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (userInfo) {
      setIsMounted(true);
    }
  }, []);

  const menuItems = [
    // { name: 'Dashboard', icon: (fill: any) => <DashboardIcon fill={fill} />, url: '/dashboard' },
    { name: 'Profile Settings', icon: () => <UserRoundCog color={pathname.startsWith('/profile-settings') ? 'white ' : '#425586'} />, url: '/profile-settings' },
    { name: 'Gallery', icon: () => <Images color={pathname.startsWith('/gallery') ? 'white ' : '#425586'} />, url: '/gallery' },
    { name: 'SlideShow', icon: (fill: any) => <WillsIcon fill={fill} />, url: '/slideshow' },
    { name: 'Timeline', icon: (fill: any) => <FaTimeline size={25} fill={fill} />, url: '/timeline' },
    { name: 'Plans & Subscriptions', icon: (fill: any) => <PlansIcon fill={fill} />, url: '/plans' },
  ];

  const adminItems = [
    { name: 'Dashboard', icon: (fill: any) => <DashboardIcon fill={fill} />, url: '/admin/dashboard' },
    { name: 'Wills', icon: (fill: any) => <WillsIcon fill={fill} />, url: '/admin/wills' },
    { name: 'users', icon: (fill: any) => <User2 color='#00AFE5' fill={fill} />, url: '/admin/users' },
    { name: 'payment recieve', icon: () => <BadgeEuro color={pathname.startsWith('/admin/payment') ? 'white ' : '#00AFE5'} />, url: '/admin/payment' },
    // { name: 'Plans & Subscriptions', icon: (fill: any) => <PlansIcon fill={fill} />, url: '/admin/plans' },

  ]

  const [isOpen, setIsOpen] = useState(false);
  const displayItems = userInfo?.role?.name === 'Admin' ? adminItems : menuItems;

  return (
    <>
      <div className="xl:hidden flex items-center px-4 py-3 bg-white shadow z-50 sticky top-0">

        <div className="flex items-center flex-col  justify-center space-x-2 gap-1">
          <Image src={'/img/logo.png'} width={80} height={100} alt="Will By Will Logo" className="" />
          <div className='text-[10px] font-semibold text-black'>My Life - My Happiness</div>
        </div>

        <div className='pl-6 ml-auto mr-4 py-3 flex justify-between items-center'>
          {
            <div className="flex items-center gap-3 relative" ref={dropdownRef}>
              <div
                className="w-[55px] h-[55px] rounded-full bg-gray-300 cursor-pointer"
                onClick={toggleDropdown}
              >
                <Image
                  src={'/img/profile.jpg'}
                  alt="profileF"
                  width={100}
                  height={100}
                  className="max-w-full h-full w-full object-cover rounded-full"
                />
              </div>
              <div className="text-right">
                {!isMounted ? <div className="space-y-2 w-full">
                  <Skeleton variant="rectangular" width={100} height={10} />
                  <Skeleton variant="rectangular" width={60} height={10} sx={{ borderRadius: '10px' }} />
                </div> : (<>
                  <p className="text-[16px] font-semibold">{userInfo?.email}</p>
                  {userInfo?.role?.name ? (

                    <p className="text-sm w-full text-left">{userInfo?.role?.name.split('_').join(" ") ?? 'Customer'}</p>
                  ) : (
                    <p className="text-sm w-full text-left">{userInfo?.role?.split('_').join(" ") ?? 'Customer'}</p>
                  )
                  }
                </>
                )}
              </div>
              {showDropdown && (
                <div className="absolute top-16 right-0 bg-white border shadow-md p-3 rounded-md w-[160px] sm:hidden z-10">
                  <p className="text-[16px] font-semibold">{userInfo?.name}</p>
                  <p className="text-sm text-gray-600">{userInfo?.role?.name}</p>
                </div>
              )}
            </div>
          }
        </div>
        <button onClick={() => setIsOpen(!isOpen)} className='cursor-pointer'>
          {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {isOpen && (
        <div
          onClick={() => setIsOpen(false)}
          className="fixed inset-0 bg-black/20 bg-opacity-30 z-40 xl:hidden"
        />
      )}

      <div className={`fixed xl:relative top-0 left-0 h-full bg-white border-r border-[#999999] shadow-xl p-4 w-[75%] sm:w-[300px] xl:w-[359px] z-50 transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'} xl:translate-x-0 xl:block`}>
        <div className="flex items-center flex-row  justify-center space-x-2 mb-2 gap-1">
          <Image src={'/img/logo.png'} width={80} height={100} alt="Will By Will Logo" className="" />
          <div className='text-[20px] font-semibold text-custom-primary'>My Life  <br /> My Happiness</div>
        </div>


        <div className="space-y-3">
          <p className="text-sm font-medium text-gray-500 uppercase mb-5">General</p>

          {!isMounted ? (
            [...Array(4)].map((_, idx) => (
              <div key={idx} className="px-4 flex gap-3 items-center py-2.5 space-y-1 w-full">
                <Skeleton variant="circular" width={24} height={24} />
                <Skeleton variant="rectangular" width={90} height={14} />
              </div>
            ))
          ) : (
            <>
              {displayItems.map((item) => (
                <div
                  key={item.name}

                  className={`flex items-center w-full font-semibold  cursor-pointer text-left space-x-2 py-2.5 text-[15px] px-4 rounded-full transition ${pathname.startsWith(item.url) ? "bg-custom-primary text-white font-medium" : "hover:text-custom-primary hover:bg-custom-secondary"
                    }`}
                  onClick={() => {
                    setIsOpen(false);
                    router.push(item.url);
                  }}
                >
                  {item.icon(pathname.startsWith(item.url) ? "#FFFFFF" : "#425586")}
                  <span>{item.name}</span>
                </div>
              ))}

              <SettingDropDown />
              <button
                onClick={handleLogoutClick}
                className='flex w-full hover:bg-custom-secondary cursor-pointer font-bold gap-4 py-2.5 text-[15px] space-x-2 px-4 hover:text-custom-primary transition rounded-full'
              >
                <LogOutIcon color='#425586' size={23} />
                <span>Logout</span>
              </button>
            </>

          )}
        </div>

      </div>

      {/* Logout Confirmation Dialog */}
      {showLogoutDialog && (
        <div className="fixed inset-0  bg-black/70 bg-opacity-50 flex items-center justify-center z-[60]">
          <div
            ref={logoutDialogRef}
            className="bg-white rounded-lg relative max-w-xl w-[90%] shadow-xl px-8 pt-10 pb-8"
          >
            {/* <h3 className="text-xl font-bold mb-4">Confirm Logout</h3> */}
            <p className="mb-6 font-conthrax text-2xl text-center">Are you sure you want to logout from your account?</p>
            <X className='absolute top-4 right-4 cursor-pointer' onClick={handleLogoutCancel} />
            <div className="flex justify-center gap-3 px-16">
              <Button
                onClick={handleLogoutCancel}
                variant='outline'
                // className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                disabled={isLoggingOut}
              >
                Cancel
              </Button>
              <Button
                onClick={handleLogoutConfirm}
                // className="px-4 py-2 bg-[#00AFE5] text-white rounded-md hover:bg-[#0095c4] transition flex items-center"
                disabled={isLoggingOut}
              >
                {isLoggingOut ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  "Yes, Logout"
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const DashboardIcon = ({ fill = "#00AFE5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className='w-[30px]' width="26" height="26" viewBox="0 0 26 26" fill="none">
    <path fillRule="evenodd" clipRule="evenodd" d="M5.41675 17.3333C5.41675 16.1367 6.3868 15.1667 7.58341 15.1667H10.8334C12.0301 15.1667 13.0001 16.1367 13.0001 17.3333V20.5833C13.0001 21.78 12.0301 22.75 10.8334 22.75H7.58341C6.3868 22.75 5.41675 21.78 5.41675 20.5833V17.3333ZM10.8334 17.3333H7.58341V20.5833H10.8334V17.3333Z" fill={fill} />
    <path fillRule="evenodd" clipRule="evenodd" d="M3.25 5.41667C3.25 4.22005 4.22005 3.25 5.41667 3.25H10.8333C12.03 3.25 13 4.22005 13 5.41667V10.8333C13 12.03 12.03 13 10.8333 13H5.41667C4.22005 13 3.25 12.03 3.25 10.8333V5.41667ZM10.8333 5.41667H5.41667V10.8333H10.8333V5.41667Z" fill={fill} />
    <path fillRule="evenodd" clipRule="evenodd" d="M15.1667 17.3333C15.1667 16.1367 16.1368 15.1667 17.3334 15.1667H20.5834C21.7801 15.1667 22.7501 16.1367 22.7501 17.3333V20.5833C22.7501 21.78 21.7801 22.75 20.5834 22.75H17.3334C16.1368 22.75 15.1667 21.78 15.1667 20.5833V17.3333ZM20.5834 17.3333H17.3334V20.5833H20.5834V17.3333Z" fill={fill} />
    <path fillRule="evenodd" clipRule="evenodd" d="M15.1667 7.58334C15.1667 6.38672 16.1368 5.41667 17.3334 5.41667H20.5834C21.7801 5.41667 22.7501 6.38672 22.7501 7.58334V10.8333C22.7501 12.03 21.7801 13 20.5834 13H17.3334C16.1368 13 15.1667 12.03 15.1667 10.8333V7.58334ZM20.5834 7.58334H17.3334V10.8333H20.5834V7.58334Z" fill={fill} />
  </svg>
);

const PlansIcon = ({ fill = "#00AFE5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className='w-[30px]' width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path d="M6.00235 2C3.80483 2 2 3.80483 2 6.00235C2 8.19985 3.80483 10.0027 6.00235 10.0027C6.73889 10.0027 7.42121 9.78651 8.01621 9.43432L14.5715 15.9838C14.3852 16.2988 14.2402 16.6393 14.1457 17.0015H9.86014C9.41083 15.2857 7.85368 14.0051 6.00235 14.0051C3.80483 14.0051 2 15.8001 2 17.9977C2 20.1952 3.80483 22 6.00235 22C7.85135 22 9.40662 20.7177 9.85819 19.0036H14.1477C14.5976 20.7177 16.1486 22 17.9977 22C20.1952 22 22 20.1952 22 17.9977C22 15.8001 20.1952 14.0051 17.9977 14.0051C17.2607 14.0051 16.5779 14.2201 15.9838 14.5715L9.43432 8.01621C9.62122 7.70047 9.76541 7.36116 9.86014 6.99854H14.1457C14.5929 8.71658 16.1458 10.0027 17.9977 10.0027C20.1952 10.0027 22 8.19985 22 6.00235C22 3.80483 20.1952 2 17.9977 2C16.1485 2 14.5974 3.28399 14.1477 4.99834H9.85819C9.40679 3.28399 7.85152 2 6.00235 2ZM6.00235 4.00215C7.11887 4.00215 8.00254 4.88582 8.00254 6.00235C8.00254 7.11887 7.11887 8.00254 6.00235 8.00254C4.88582 8.00254 4.00215 7.11887 4.00215 6.00235C4.00215 4.88582 4.88582 4.00215 6.00235 4.00215ZM17.9977 4.00215C19.1142 4.00215 19.9998 4.88582 19.9998 6.00235C19.9998 7.11887 19.1142 8.00254 17.9977 8.00254C16.8811 8.00254 15.9975 7.11887 15.9975 6.00235C15.9975 4.88582 16.8811 4.00215 17.9977 4.00215ZM6.00235 15.9975C7.11887 15.9975 8.00254 16.8811 8.00254 17.9977C8.00254 19.1142 7.11887 19.9998 6.00235 19.9998C4.88582 19.9998 4.00215 19.1142 4.00215 17.9977C4.00215 16.8811 4.88582 15.9975 6.00235 15.9975ZM17.9977 15.9975C19.1142 15.9975 19.9998 16.8811 19.9998 17.9977C19.9998 19.1142 19.1142 19.9998 17.9977 19.9998C16.8811 19.9998 15.9975 19.1142 15.9975 17.9977C15.9975 16.8811 16.8811 15.9975 17.9977 15.9975Z" fill={fill} />
  </svg>
);

const WillsIcon = ({ fill = "#00AFE5" }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className='w-[30px]' width="32" height="32" viewBox="0 0 32 32" fill="none">
    <path d="M24.9125 10.575C24.8563 10.4375 24.7875 10.3062 24.6938 10.1937L19.4562 4.46249C19.3375 4.33124 19.1938 4.23749 19.0375 4.15624C18.8563 4.06249 18.6562 4.02499 18.4438 4.02499H8.45C7.675 4.02499 7.03125 4.68749 7.03125 5.51874V26.4687C7.03125 27.3062 7.675 28.025 8.45 28.025H23.6437C24.4187 28.025 25.0312 27.3062 25.0312 26.4687V11.2125C25.0312 10.9875 25 10.7687 24.9125 10.575ZM10.0312 11.1625C10.0312 11.0687 10.1437 11.0312 10.2437 11.0312H14.6687C14.7687 11.0312 14.8438 11.0687 14.8438 11.1625V11.8375C14.8438 11.925 14.775 12.0312 14.6687 12.0312H10.2437C10.1437 12.0312 10.0312 11.925 10.0312 11.8375V11.1625ZM10.0312 19.1625C10.0312 19.0687 10.1437 19.0312 10.2437 19.0312H17.8813C17.9813 19.0312 18.0312 19.0687 18.0312 19.1625V19.8375C18.0312 19.925 17.9875 20.0312 17.8813 20.0312H10.2437C10.1437 20.0312 10.0312 19.925 10.0312 19.8375V19.1625ZM20.0312 23.8375C20.0312 23.925 19.9875 24.0312 19.8813 24.0312H10.2437C10.1437 24.0312 10.0312 23.925 10.0312 23.8375V23.1625C10.0312 23.0687 10.1437 23.0312 10.2437 23.0312H19.8813C19.9813 23.0312 20.0312 23.0687 20.0312 23.1625V23.8375ZM22.0312 15.8375C22.0312 15.925 21.9875 16.0312 21.8813 16.0312H10.2437C10.1437 16.0312 10.0312 15.925 10.0312 15.8375V15.1625C10.0312 15.0687 10.1437 15.0312 10.2437 15.0312H21.8813C21.9813 15.0312 22.0312 15.0687 22.0312 15.1625V15.8375ZM19.1 11.0937C18.75 11.0937 18.4062 10.7687 18.4062 10.3875V6.26249L22.8563 11.0937H19.1Z" fill={fill} />
  </svg>
);


export default Sidebar;




