'use client';

import React, { useEffect, useState } from 'react'
import Cookies from 'js-cookie';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { getUserInfoData } from '@/app/store/reducers/life.selector';
import { User, Image, Play, Clock, CreditCard, Settings, Users, Lock, LogOut, ChevronRight, ChevronLeft, Plus, Mail, Check, X, Shield, UserPlus, Search, Filter, MoreVertical, Edit2, Trash2, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import appServiceInstance from '@/app/services/AppServices';
import { setRole, setUserInfo } from '@/app/store/reducers/life.reducer';

const SidebarV1 = () => {
    const router = useRouter();
    const pathname = usePathname();
    const dispatch = useAppDispatch();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [isMounted, setIsMounted] = useState(false)

    const userInfo = useAppSelector(getUserInfoData);
    const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
    const [activeSection, setActiveSection] = React.useState('profile');
    const [activeSubSection, setActiveSubSection] = React.useState('contacts');
    const [showLogoutDialog, setShowLogoutDialog] = useState(false);
    const logoutDialogRef = React.useRef(null);


    const handleLogoutClick = () => {
        setShowLogoutDialog(true);
    };

    const handleLogoutConfirm = async () => {
        try {
            setIsLoggingOut(true);
            const response = await appServiceInstance.signOut();

            if (response.status === 200) {
                // Clear cookies
                Cookies.remove('token');
                Cookies.remove('expiryTime');
                Cookies.remove('role');
                dispatch(setUserInfo(null));
                dispatch(setRole(""));
                toast.success("Logged out successfully");

                // Redirect to login page
            } else {
                Cookies.remove('token');
                Cookies.remove('expiryTime');
                Cookies.remove('role');
                router.push('/login');
                toast.success("Logged out successfully");
            }
        } catch (error) {
            console.error("Logout error:", error);
            toast.error("An error occurred during logout");
        } finally {
            setIsLoggingOut(false);
            setShowLogoutDialog(false);
        }
    };

    const handleLogoutCancel = () => {
        setShowLogoutDialog(false);
    };

    // useEffect(() => {
    //     const handleClickOutside = (event: MouseEvent) => {
    //         if (
    //             dropdownRef.current &&
    //             !(dropdownRef.current as any).contains(event.target)
    //         ) {
    //             setShowDropdown(false);
    //         }
    //         if (
    //             logoutDialogRef.current &&
    //             showLogoutDialog &&
    //             !(logoutDialogRef.current as any).contains(event.target)
    //         ) {
    //             setShowLogoutDialog(false);
    //         }
    //     };

    //     document.addEventListener('mousedown', handleClickOutside);
    //     return () => {
    //         document.removeEventListener('mousedown', handleClickOutside);
    //     };
    // }, []);

    useEffect(() => {
        if (userInfo) {
            setIsMounted(true);
        }
    }, []);

    const sidebarItems = [
        { id: 'profile', label: 'Profile Settings', icon: User, url: '/profile-settings' },
        { id: 'gallery', label: 'Gallery', icon: Image, url: '/gallery' },
        { id: 'slideshow', label: 'SlideShow', icon: Play, url: '/slideshow' },
        { id: 'timeline', label: 'Timeline', icon: Clock, url: '/timeline' },
        { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard, url: '/plans' },
        {
            id: 'settings',
            label: 'Settings',
            icon: Settings,
            subItems: [
                { id: 'contacts', label: 'Invite Trusted Contacts', icon: Users, url: '/trusted-contacts' },
                { id: 'password', label: 'Update Password', icon: Lock, url: '/update-password' },
                { id: 'payment', label: 'Payment Settings', icon: CreditCard, url: '/payment-settings' }
            ]
        }
    ];
    const adminItems = [
        { id: 'profile', label: 'Profile Settings', icon: User },
        { id: 'gallery', label: 'Gallery', icon: Image },
        { id: 'slideshow', label: 'SlideShow', icon: Play },
        { id: 'timeline', label: 'Timeline', icon: Clock },
        { id: 'plans', label: 'Plans & Subscriptions', icon: CreditCard },
    ];
    const displayItems = userInfo?.role?.name === 'Admin' ? adminItems : sidebarItems;


    return (
        <div>
            <div className={`${sidebarCollapsed ? 'w-20' : 'w-80'} bg-gradient-to-b from-blue-600 to-blue-800 h-screen sticky top-0 shadow-2xl transition-all duration-300`}>
                {/* Logo Section */}
                <div className="p-6 relative">
                    <button
                        onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
                        className="absolute -right-3 top-8 w-8 h-8 bg-white rounded-full flex items-center justify-center text-blue-600 hover:bg-gray-100 transition-colors duration-200 shadow-lg z-10"
                    >
                        {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
                    </button>
                    <div className={`flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'}`}>
                        <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                            <svg className="w-8 h-8" viewBox="0 0 100 100">
                                <circle cx="30" cy="40" r="12" fill="#2563EB" />
                                <circle cx="70" cy="40" r="12" fill="#2563EB" />
                                <path d="M 30 65 Q 50 80 70 65" stroke="#2563EB" strokeWidth="3" fill="none" />
                            </svg>
                        </div>
                        {!sidebarCollapsed && (
                            <div>
                                <h1 className="text-xl font-semibold text-white">My Life</h1>
                                <p className="text-sm text-blue-200">My Happiness</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Navigation */}
                <nav className="px-4">
                    {!sidebarCollapsed && <p className="text-xs font-semibold text-blue-200 uppercase tracking-wider mb-4 px-4">GENERAL</p>}
                    <div className="space-y-2">
                        {displayItems.map((item: any) => (
                            <div key={item.id}>
                                <button
                                    onClick={() => {
                                        setActiveSection(item.id);
                                        if (item.id === 'settings') setActiveSubSection('');
                                        else router.push(item?.url);
                                    }}
                                    className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 rounded-xl transition-all duration-300 ${activeSection === item.id
                                        ? 'bg-white/20 text-white shadow-lg'
                                        : 'text-blue-100 hover:bg-white/10 hover:text-white'
                                        }`}
                                    title={sidebarCollapsed ? item.label : ''}

                                >
                                    <item.icon className="w-5 h-5" />
                                    {!sidebarCollapsed && <span className="font-medium">{item.label}</span>}
                                    {!sidebarCollapsed && item.subItems && <ChevronRight className="w-4 h-4 ml-auto" />}
                                </button>

                                {/* Submenu */}
                                {!sidebarCollapsed && item.subItems && activeSection === item.id && (
                                    <div className="ml-4 mt-1 space-y-1">
                                        {item.subItems.map((subItem: any) => (
                                            <button
                                                key={subItem.id}
                                                onClick={() => { setActiveSubSection(subItem.id); router.push(subItem.url); }}
                                                className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg transition-all duration-200 ${activeSubSection === subItem.id
                                                    ? 'bg-white/20 text-white'
                                                    : 'text-blue-200 hover:bg-white/10 hover:text-white'
                                                    }`}
                                            >
                                                <subItem.icon className="w-4 h-4" />
                                                <span className="text-sm">{subItem.label}</span>
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Logout Button */}
                    <button onClick={handleLogoutClick} className={`w-full flex items-center ${sidebarCollapsed ? 'justify-center' : 'space-x-3'} px-4 py-3 mt-8 text-blue-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200`}>
                        <LogOut className="w-5 h-5" />
                        {!sidebarCollapsed && <span className="font-medium">Logout</span>}
                    </button>
                </nav>

                {showLogoutDialog && (
                    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                            <div className="p-6 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-xl font-bold text-gray-800">Add New Contact</h3>
                                    <button
                                        onClick={() => setShowLogoutDialog(false)}
                                        className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                                    >
                                        <X className="w-4 h-4 text-gray-600" />
                                    </button>
                                </div>
                            </div>
                            {/* <div className="p-6 space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <input
                                  type="text"
                                  value={newContact.name}
                                  onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
                                  placeholder="Enter contact's full name"
                                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <input
                                  type="email"
                                  value={newContact.email}
                                  onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
                                  placeholder="Enter contact's email"
                                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Role/Relationship</label>
                                <select
                                  value={newContact.role}
                                  onChange={(e) => setNewContact({ ...newContact, role: e.target.value })}
                                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                >
                                  <option value="">Select a role</option>
                                  <option value="Family Member">Family Member</option>
                                  <option value="Legal Representative">Legal Representative</option>
                                  <option value="Financial Advisor">Financial Advisor</option>
                                  <option value="Healthcare Proxy">Healthcare Proxy</option>
                                  <option value="Other">Other</option>
                                </select>
                              </div>
                            </div> */}
                            <div className="p-6 border-t border-gray-200 flex gap-3">
                                <button
                                    onClick={() => setShowLogoutDialog(false)}
                                    className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleLogoutConfirm}
                                    disabled={isLoggingOut}
                                    className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Logout
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default SidebarV1