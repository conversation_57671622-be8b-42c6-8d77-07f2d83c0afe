
import { useEffect, useState } from 'react'
import appServiceInstance from '../services/AppServices';
import { useAppDispatch } from '../store/hooks';
import { setUserInfo } from '../store/reducers/life.reducer';
import Cookies from 'js-cookie';

const UserGetUserInfo = () => {
    const dispatch = useAppDispatch();
    const [data, setData] = useState(null); // Changed from [] to null for better type checking
    const [loading, setLoading] = useState(true); // Start with loading true
    const [error, setError] = useState(null);

    const fetchUserInfo = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await appServiceInstance.getUserInfo();

            if (response?.status === 200) {
                setData(response.data.data);
                dispatch(setUserInfo(response.data?.data));
            } else if (response?.status === 401) {
                // Handle unauthorized - clear cookies and localStorage
                Object.keys(Cookies.get()).forEach(cookieName => {
                    Cookies.remove(cookieName);
                });
                localStorage.clear();
                console.log("Unauthorized, cleared cookies and localStorage");
            } else {
                console.log("Unexpected response status:", response?.status);
            }
        } catch (error: any) {
            console.error("Error fetching user info:", error);
            setError(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Check if token exists before fetching
        const token = Cookies.get('token');
        if (token) {
            fetchUserInfo();
        } else {
            console.log("No token found, skipping user info fetch");
            setLoading(false);
        }
    }, []);

    // Add a refetch method to allow manual refetching
    const refetch = () => {
        fetchUserInfo();
    };

    return { data, loading, error, refetch };
}

export default UserGetUserInfo;
