'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import InputWithLabel from '@/app/components/ui/InputWithLabel';
import { Check } from 'lucide-react';
import Button from '@/app/components/ui/Button';
import { z } from 'zod';
import appServiceInstance from '@/app/services/AppServices';
// import { setRole   } from '@/app/store/reducers/willByWill.reducer';
import Cookies from 'js-cookie';
import Link from 'next/link';
import { useAppDispatch } from '@/app/store/hooks';
import { setRole, setUserInfo } from '@/app/store/reducers/life.reducer';

const LoginSchema = z.object({
  email: z.string().email("Please enter a valid email Address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

type LoginData = z.infer<typeof LoginSchema>;

const Login = () => {

  const router = useRouter();
  const dispatch = useAppDispatch();
  const [checked, setChecked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginError, setLoginError] = useState<LoginErrors>({});
  const [loginData, setLoginData] = useState<LoginData>({
    email: "",
    password: "",

  });

  const setToken = (data: any) => {
    Cookies.set("token", data?.accessToken);
    Cookies.set("expiryTime", data?.expiresIn);
    Cookies.set('role', data?.user?.role);
  };

  // handleInputChange
  const handleInputChnage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setLoginData((prev) => {
      return {
        ...prev,
        [id]: value
      }
    })

    // clears error when user types
    if (loginError[id as keyof LoginErrors]) {
      setLoginError((prev) => {
        return {
          ...prev,
          [id]: undefined
        }
      })
    }
  }

  // handleBlur
  const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    try {
      LoginSchema.shape[id as keyof LoginData].parse(value);
      setLoginError((prev) => {
        return {
          ...prev,
          [id]: undefined
        }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        setLoginError((prev) => {
          return {
            ...prev,
            [id]: error.errors[0].message
          }
        })
      }
    }
  }
  // handleSubmit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // check inputs are there 
    try {
      LoginSchema.parse(loginData);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path[0];
          if (path) {
            newErrors[path] = err.message;
          }
        });
        setLoginError(newErrors);
        setLoading(false);
        return;
      }
    }

    if (!checked) {
      setLoginError((prev) => {
        return {
          ...prev,
          api: "Please agree to the terms and conditions"
        }
      })
      setLoading(false);
      return;
    }

    try {
      const response = await appServiceInstance.signIn(loginData);
      if (response?.status === 200) {
        setToken(response?.data?.data);
        dispatch(setUserInfo(response.data.data.user));
        dispatch(setRole(response?.user?.role));
        router.push('/dashboard'); // Example redirect

      } else if (response?.status === 401) {
        setLoginError((prev) => {
          return {
            ...prev,
            api: "Invalid email or password"
          }
        })
      } else {
        setLoginError((prev) => ({
          ...prev,
          api: response.data?.message || "Something went wrong"
        }))
      }

    } catch (error) {
      console.error(error as any || "something went wrong!!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div >
      {/* Form Section */}
      <div className="">
        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Email Address */}
          <InputWithLabel id='email' type='email' placeholder='<EMAIL>' label='Email Address' onChange={handleInputChnage} onBlur={handleBlur} error={loginError.email} />

          {/* Password */}
          <InputWithLabel id='password' type='password' placeholder='********' label='Password' onChange={handleInputChnage} onBlur={handleBlur} error={loginError.password} />

          {/* check box */}
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-start gap-2 cursor-pointer" onClick={() => {
              setLoginError((prev) => ({
                ...prev,
                api: undefined
              }));
              setChecked(!checked)
            }}>
              <div
                className={`w-5 h-5 flex items-center justify-center rounded border transition-colors
          ${checked ? 'bg-custom-primary border-custom-primary' : 'bg-white border-gray-300'}`}
              >
                {checked && <Check size={16} strokeWidth={3} className="text-white" />}
              </div>
              <label className="text-sm text-gray-700 leading-snug select-none">
                I agree to the{' '}
                <a href="/terms" className="text-custom-primary underline hover:text-blue-800">
                  Terms & Privacy Policy
                </a>
              </label>
            </div>
            <Link href='/forgot-password' className='underline text-sm cursor-pointer hover:text-blue-800'>forgot password</Link>
          </div>

          <div>
            {
              loginError?.api && (

                loginError?.api === 'Email not verified' ? (
                  <Link href='/resend-verification' className='underline text-sm text-red-500 cursor-pointer hover:text-red-800'>Resend Verification Email</Link>
                ) : (
                  <>
                    <p className="text-sm text-red-500 ">{loginError.api}</p>
                  </>)
              )
            }
          </div>

          <Button isLoading={loading} >Sign In</Button>

          <div className="text-sm text-left">
            Don&apos;t have an account?{' '}
            <Link href="/signup" className="text-custom-primary underline">
              Sign Up
            </Link>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 mt-10">
          © {new Date().getFullYear()} Will By Will
        </div>
      </div>

      {/* Graphic Section */}

    </div>
  );
};

export default Login;