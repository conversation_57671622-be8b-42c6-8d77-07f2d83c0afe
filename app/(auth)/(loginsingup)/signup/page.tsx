'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import InputWith<PERSON>abel from '@/app/components/ui/InputWithLabel';
import { Check } from 'lucide-react';
import Button from '@/app/components/ui/Button';
import Link from 'next/link';
import { z } from 'zod';
import appServiceInstance from '@/app/services/AppServices';

// Define individual field schemas for validation during blur events
const fieldSchemas = {
    fullName: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Please enter a valid email Address"),
    password: z
        .string()
        .min(6, "Password must be at least 6 characters")
        .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
        .regex(/[a-z]/, "Password must contain at least one lowercase letter")
        .regex(/[0-9]/, "Password must contain at least one number"),
    confirmPassword: z.string().min(6, "Password must be at least 6 characters")
};

// Complete schema for form submission validation
const SignupSchema = z.object({
    fullName: fieldSchemas.fullName,
    email: fieldSchemas.email,
    password: fieldSchemas.password,
    confirmPassword: fieldSchemas.confirmPassword
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
});

type SignupData = z.infer<typeof SignupSchema>;

// Define error interface
interface SignupErrors {
    fullName?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
    api?: string;
}


const Signup: React.FC = () => {

    const router = useRouter();
    const [checked, setChecked] = useState(false);
    const [loading, setLoading] = useState(false);
    const [signupData, setSignupData] = useState<SignupData>({
        fullName: "",
        email: "",
        password: "",
        confirmPassword: "",
    });
    const [signupError, setSignupError] = useState<SignupErrors>({});

    // handleInputChange
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target;
        setSignupData((prev) => ({
            ...prev,
            [id]: value
        }));

        // clears error when user types
        if (signupError[id as keyof SignupErrors]) {
            setSignupError((prev) => ({
                ...prev,
                [id]: undefined
            }));
        }
    }

    // handleBlur
    const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target;

        try {
            // Validate the field using its individual schema
            if (id in fieldSchemas) {
                fieldSchemas[id as keyof typeof fieldSchemas].parse(value);
            }

            // Additional check for confirmPassword
            if (id === 'confirmPassword' && value !== signupData.password) {
                throw new Error("Passwords do not match");
            }

            // Clear error if validation passes
            setSignupError((prev) => ({
                ...prev,
                [id]: undefined
            }));
        } catch (error) {
            let errorMessage = "Invalid input";

            if (error instanceof z.ZodError) {
                errorMessage = error.errors[0].message;
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            setSignupError((prev) => ({
                ...prev,
                [id]: errorMessage
            }));
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            SignupSchema.parse(signupData);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const newErrors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    const path = err.path[0];
                    if (path) {
                        newErrors[path] = err.message;
                    }
                });
                setSignupError(newErrors);
                setLoading(false);
                return;
            }
        }

        if (!checked) {
            setSignupError((prev) => ({
                ...prev,
                api: "Please agree to the terms and conditions"
            }));
            setLoading(false);
            return;
        }

        const payload = {
            name: signupData.fullName,
            email: signupData.email,
            password: signupData.password,
            confirmPassword: signupData.confirmPassword,
            phoneNumber: "12312312112"
        }

        try {
            const response = await appServiceInstance.signUp(payload);
            if (response?.status === 201) {
                router.push('/login');
            } else {
                setSignupError((prev) => ({
                    ...prev,
                    api: response.data?.message || "Something went wrong"
                }));
            }
        } catch (err) {
            console.log(err);
        } finally {
            setLoading(false);
        }

    };

    return (
        <div >
            {/* Form Section */}
            <div className="">

                {/* Form */}
                <form onSubmit={handleSubmit} className="flex flex-col gap-4">

                    <div className='flex w-full gap-2'>
                        <InputWithLabel id='fullName' type='text' placeholder='John Doe' label='Full Name' onChange={handleInputChange} onBlur={handleBlur} error={signupError?.fullName} />
                        <InputWithLabel id='email' type='email' placeholder='<EMAIL>' label='Email Address' onBlur={handleBlur} onChange={handleInputChange} error={signupError.email} />

                    </div>

                    {/* Password */}
                    <InputWithLabel id='password' type='password' placeholder='**********' label='Password' onChange={handleInputChange} onBlur={handleBlur} error={signupError.password} />

                    {/* Confirm Password */}
                    <InputWithLabel id='confirmPassword' type='password' placeholder='********' label='Confirm Password' onChange={handleInputChange} onBlur={handleBlur} error={signupError.confirmPassword} />

                    {/* check box */}
                    <div className="flex items-start gap-2">
                        <div className="flex items-start gap-2 cursor-pointer" onClick={() => {
                            setChecked(!checked); setSignupError((prev) => ({
                                ...prev,
                                api: undefined
                            }));
                        }}>
                            <div
                                className={`w-5 h-5 flex items-center justify-center rounded border transition-colors
          ${checked ? 'bg-custom-primary border-custom-primary' : 'bg-white border-gray-300'}`}
                            >
                                {checked && <Check size={16} strokeWidth={3} className="text-white" />}
                            </div>
                            <label className="text-sm text-gray-700 leading-snug select-none">
                                I agree to the{' '}
                                <a href="/terms" className="text-custom-primary underline hover:text-blue-700">
                                    Terms & Privacy Policy
                                </a>
                            </label>
                        </div>
                    </div>


                    {
                        signupError?.api && (
                            <>
                                <p className="text-sm text-red-500 ">{signupError.api}</p>
                            </>
                        )
                    }

                    <Button isLoading={loading} >Sign Up & Get Started</Button>

                    <div className="text-sm text-left">
                        Already have an account?{' '}
                        <Link href="/login" className="text-custom-primary underline">
                            Sign In
                        </Link>
                    </div>
                </form>

                {/* Footer */}
                <div className="text-center text-xs text-gray-500 mt-10">
                    © {new Date().getFullYear()} Will By Will
                </div>
            </div>

            {/* Graphic Section */}

        </div>
    );
};

export default Signup;
