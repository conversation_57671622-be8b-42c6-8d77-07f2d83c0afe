'use client';

import appServiceInstance from "@/app/services/AppServices";
import { useGoogleLogin } from "@react-oauth/google";
import Image from "next/image";
import { usePathname } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { FcGoogle } from "react-icons/fc";
import { FaFacebookSquare } from "react-icons/fa";

const LoginSocialFacebook = dynamic(
    () => import("reactjs-social-login").then((mod) => mod.LoginSocialFacebook),
    { ssr: false }
);


export default function LoginSignupLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    const router = useRouter();
    const page = pathname.split("/")[1];
    const [socialLoading, setSocialLoading] = useState(false);

    const setToken = (token: string, expiryToken: any) => {
        Cookies.set("token", token);
        Cookies.set("expiryToken", expiryToken);
    };

    const handleSocialLogin = async (provider: string, data: any) => {
        try {
            setSocialLoading(true);

            let response;
            if (provider === "google") {
                const payload = {
                    access_token: data?.access_token,
                    sub: data?.sub,
                    name: data?.name,
                    given_name: data?.given_name,
                    picture: data?.picture,
                    email: data?.email,
                    email_verified: true
                };
                response = await appServiceInstance.loginWithGoogle(payload);
            } else {
                response = await appServiceInstance.loginWithFacebook(data);
            }

            if (response.status === 200) {
                setToken(response.data.data.accessToken, response.data.data.expiresIn);
                console.log(response.data)
                toast.success(`Successfully logged in with ${provider}`);
                router.push("/dashboard");
            } else {
                toast.error(response.data?.message || `Failed to login with ${provider}`);
            }
        } catch (error: any) {
            console.error(`${provider} login error:`, error);
            toast.error(`Failed to login with ${provider}`);
        } finally {
            setSocialLoading(false);
        }
    };

    const login = useGoogleLogin({
        onSuccess: async (tokenResponse) => {
            try {
                const userInfo = await fetch(
                    "https://www.googleapis.com/oauth2/v3/userinfo",
                    {
                        headers: {
                            Authorization: `Bearer ${tokenResponse.access_token}`,
                        },
                    }
                ).then((res) => res.json());

                if (!userInfo.email || !userInfo.name) {
                    throw new Error("Incomplete user data");
                }

                handleSocialLogin("google", {
                    access_token: tokenResponse?.access_token,
                    sub: userInfo?.sub,
                    name: userInfo?.name,
                    given_name: userInfo?.given_name,
                    picture: userInfo.picture,
                    email: userInfo.email,
                    email_verified: true
                });
            } catch (error) {
                console.error("Google login error:", error);
                toast.error("Failed to fetch user information");
            }
        },
        onError: () => toast.error("Google login failed"),
        scope: "email profile",
    });

    return (
        <div className=" flex h-screen justify-center items-center">
            {/* <Image src="/img/flower-bg.png" className="absolute top-0 right-0 z-10" alt="Laptop Graphic" width={490} height={400} priority /> */}
            {/* <Image src="/img/bird.png" className="absolute top-0 right-0 z-10" alt="Laptop Graphic" width={60} height={400} priority /> */}
            {/* <Image src="/img/bird.png" className="absolute left-10 right-24 z-10" alt="Laptop Graphic" width={50} height={400} priority /> */}
            {/* <Image src="/img/bird.png" className="absolute bottom-10 right-32 z-10" alt="Laptop Graphic" width={60} height={400} priority /> */}
            {/* <Image src="/img/bird.png" className="absolute bottom-20 left-42 z-10" alt="Laptop Graphic" width={60} height={400} priority /> */}

            <div className="bg-white grid h-[calc(100vh-30px)] md:h-auto place-items-center z-20 w-[85%] md:w-[95%] max-w-[1200px] mx-auto grid-cols-1 md:grid-cols-2 rounded-xl overflow-hidden">
                <div className="w-full p-5 md:p-5 max-w-[470px] mx-auto">
                    <div className="text-2xl md:text-4xl font-bold text-left font-conthrax">
                        {page !== 'login' ? "Create Your Account" : 'Welcome Back'}
                    </div>
                    <div className="text-base md:text-lg mb-6 text-left">
                        {page === 'login' ? 'Sign in to continue.' : 'Sign your will in a just few steps.'}
                    </div>

                    {/* Social Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 mb-4">
                        <button
                            onClick={() => login()}
                            disabled={socialLoading}
                            className="flex items-center justify-center gap-2 h-12 border border-gray-300 rounded-full px-4 font-semibold text-sm md:text-base hover:bg-gray-100 disabled:opacity-70 disabled:cursor-not-allowed"
                        >
                            <FcGoogle size={28} />
                            Sign in with Google
                        </button>

                        <LoginSocialFacebook
                            appId="***************"
                            onResolve={({ data }: any) => {
                                if (data) {
                                    console.log('Facebook login data:', data);
                                    handleSocialLogin("facebook", data);
                                } else {
                                    toast.error("Facebook login failed");
                                }
                            }}
                            onReject={(error: any) => {
                                console.error("Facebook login error:", error);
                                toast.error("Facebook login failed");
                            }}
                        >
                            <button
                                disabled={socialLoading}
                                className="flex items-center justify-center gap-2 h-12 border border-gray-300 rounded-full px-4 font-semibold text-sm md:text-base hover:bg-gray-100 disabled:opacity-70 disabled:cursor-not-allowed"
                            >
                                <FaFacebookSquare size={28} />
                                Sign in with Facebook
                            </button>
                        </LoginSocialFacebook>
                    </div>

                    {/* Divider */}
                    <div className="flex items-center my-4">
                        <hr className="flex-grow border-gray-300" />
                        <span className="mx-4 text-gray-500 text-sm font-medium">OR</span>
                        <hr className="flex-grow border-gray-300" />
                    </div>

                    {children}
                </div>
                <div className="hidden md:flex flex-col h-full w-full relative justify-center items-center z-20 gap-8 bg-[url('/img/steps-bg.png')] bg-cover bg-center rounded-r-xl">
                    <div className="flex flex-col items-center">

                        <Image src="/img/logo.png" alt="Will By Will Logo" width={150} height={100} priority />

                        <Image src="/img/login.svg" className="" alt="Laptop Graphic" width={400} height={400} priority />
                    </div>
                </div>
            </div>
        </div>
    );
}
