"use client";
import { isNull, isUndefined } from "lodash";
import { RefreshCcw } from "lucide-react";
import { useRouter } from "next/navigation";
import React,{useState, useEffect} from "react";


export default function Page() {
    const router = useRouter();
    const [storedEmail, setStoredEmail] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true); // Track loading state


      useEffect(() => {
        if (!isUndefined(typeof window)) {
          const emailFromStorage = localStorage.getItem("reset-email");
          setStoredEmail(emailFromStorage);
          setIsLoading(false); // Once localStorage is accessed, stop loading
        }
      }, []);

      useEffect(()=>{
        if(!isLoading  && isNull(storedEmail)){
            router.push("/login")
        }
      },[storedEmail, isLoading])
      
  return (
      <div className="w-full max-w-md p-8 text-center bg-white rounded-lg shadow-lg">
        <div className="mb-6">
          <div className="w-12 h-12 bg-emerald-50 rounded-full flex items-center justify-center mx-auto">
            <RefreshCcw className="w-6 h-6 text-emerald-500" />
          </div>
        </div>

        <h1 className="text-xl font-semibold font-conthrax text-gray-900 mb-4">Please check your email</h1>

        <p className="text-gray-600 mb-2">You&apos;re almost there! We sent an email to</p>
        <p className="text-gray-900 font-medium bg-gray-200 rounded-4xl py-2 mb-4">{storedEmail}</p>

        <p className="text-gray-600 text-sm mb-1">Just click on the link in that email to reset your password.</p>
        <p className="text-gray-600 text-sm mb-6">If you don&apos;t see it, you may need to check your spam folder.</p>

    </div>
  )
}

