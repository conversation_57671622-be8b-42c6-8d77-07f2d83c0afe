'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import React, { useState } from 'react';
import { z } from 'zod';
import Button from '@/app/components/ui/Button';
import InputWithLabel from '@/app/components/ui/InputWithLabel';
import { toast } from 'sonner';
import appServiceInstance from '@/app/services/AppServices';


// Constants
const API_ERROR_RESPONSE_MSG = 'Something went wrong. Please try again.';
const RESET_PASSWORD_OTP_SENT_MSG = 'Password reset email sent successfully.';

// Validation schema
const emailSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
});

// type EmailData = z.infer<typeof emailSchema>;

const Recover = () => {
    const router = useRouter();
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({
        email: '',
        api: '',
    });

    const handleBlur = () => {
        const result = emailSchema.safeParse({ email });
        if (!result.success) {
            const emailError = result.error.errors.find((err) => err.path[0] === 'email');
            setErrors((prev) => ({
                ...prev,
                email: emailError ? emailError.message : '',
            }));
        } else {
            setErrors((prev) => ({ ...prev, email: '' }));
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newEmail = e.target.value;
        setEmail(newEmail);

        const result = emailSchema.safeParse({ email: newEmail });
        if (!result.success) {
            const emailError = result.error.errors.find((err) => err.path[0] === 'email');
            setErrors((prev) => ({
                ...prev,
                email: emailError ? emailError.message : '',
            }));
        } else {
            setErrors((prev) => ({ ...prev, email: '' }));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setErrors({ email: '', api: '' });


        const result = emailSchema.safeParse({ email });
        if (!result.success) {
            const emailError = result.error.errors.find((err) => err.path[0] === 'email');
            setErrors({ email: emailError ? emailError.message : '', api: '' });
            return;
        }

        try {
            setLoading(true);
            const response = await appServiceInstance.ForgotPassword({ email });

            if (response?.status === 200) {
                toast.success(RESET_PASSWORD_OTP_SENT_MSG);
                router.push('/forgot-password/sent-mail')
                localStorage.setItem("reset-email", email);
            } else{
                setErrors({ email: '', api: response.data?.message || API_ERROR_RESPONSE_MSG });
            }
        } catch (error: any) {
            const errorMsg =
                error?.data?.message ||
                error?.data?.errors?.[0]?.email ||
                API_ERROR_RESPONSE_MSG;

            setErrors({ email: '', api: errorMsg });
            toast.error(errorMsg);
        } finally {
            setLoading(false);
        }
    };

    return (
        <section className="min-w-lg">
            <div className="container-fluid">
                <div className="row login-container">
                    <div className="col-md-12 flex w-full flex-col justify-center">
                        <div className="login-form recover mx-auto my-0 w-full max-w-[658px] rounded-[10px] bg-white px-4 py-8 md:px-[78px] md:py-[73px]">
                            <div className="flex flex-col items-center justify-center text-center">
                                <Image
                                    alt="Logo"
                                    className="logo mb-3"
                                    src="/img/logo.png"
                                    width={150}
                                    height={80}
                                />
                                <h1 className="mb-[10px] text-lg md:mb-[18px] font-bold md:text-[22px] font-conthrax lg:text-[25px]">
                                    Recover Your Account
                                </h1>
                            </div>

                            <form onSubmit={handleSubmit} className="flex flex-col gap-3">
                                <div>
                                    <InputWithLabel
                                        type="email"
                                        placeholder="<EMAIL>"
                                        id="email"
                                        name="email"
                                        label="Email Address"
                                        className="!bg-[#e8f0fe]"
                                        value={email}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        error={errors.email}
                                    />
                                    <div className="min-h-[20px]">
                                        {errors.api && (
                                            <div className="p-1 text-red-500">{errors.api}</div>
                                        )}
                                    </div>
                                </div>

                                <Button type="submit" disabled={loading}>
                                    {loading ? 'Sending...' : 'Send Reset Password Link'}
                                </Button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Recover;
