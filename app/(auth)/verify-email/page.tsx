'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Cookies from 'js-cookie';
import appServiceInstance from '@/app/services/AppServices';
import Button from '@/app/components/ui/Button';

const VerifyEmailPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Verifying your email...');

  useEffect(() => {
    const verifyEmail = async () => {
      const token = searchParams.get('token');
      
      if (!token) {
        setVerificationStatus('error');
        setMessage('Verification token is missing. Please check your email link.');
        return;
      }
      try {
        const response = await appServiceInstance.verifyUserEmail(token);
        if (response.status === 200) {
          // Store token if provided in the response
          if (response.data?.data?.accessToken) {
            Cookies.set('token', response.data.data.accessToken);
          }
          
          setVerificationStatus('success');
          setMessage(response.data?.message || 'Email verified successfully!');
          
          // Redirect to dashboard after 3 seconds
          setTimeout(() => {
            router.push('/dashboard');
          }, 3000);
        } else {
          setVerificationStatus('error');
          setMessage(response.data?.message || 'Failed to verify email. The link may be expired or invalid.');
        }
      } catch (error:any) {
        setVerificationStatus('error');
        setMessage(error?.message || 'An error occurred during verification. Please try again later.');
      }
    };

    verifyEmail();
  }, [searchParams, router]);

  return (
    // <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Logo */}
        <div className="mb-6 flex justify-center">
          <Image 
            src="/img/logo.png" 
            alt="Will By Will Logo" 
            width={350} 
            height={50}
            className="h-12 w-auto"
          />
        </div>

        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          {verificationStatus === 'loading' && (
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
          )}
          {verificationStatus === 'success' && (
            <div className="rounded-full h-16 w-16 bg-green-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {verificationStatus === 'error' && (
            <div className="rounded-full h-16 w-16 bg-red-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          {verificationStatus === 'loading' && 'Verifying Your Email'}
          {verificationStatus === 'success' && 'Email Verified!'}
          {verificationStatus === 'error' && 'Verification Failed'}
        </h1>

        {/* Message */}
        <p className="text-gray-600 mb-6">{message}</p>

        {/* Action Buttons */}
        <div className="space-y-3">
          {verificationStatus === 'success' && (
            <Button onClick={() => router.push('/login')} className="w-full">
              Go to Login
            </Button>
          )}
          
          {verificationStatus === 'error' && (
            <>
              <Button onClick={() => router.push('/login')} >
                Go to Login
              </Button>
              <div onClick={() => router.push('/resend-verification')} className='underline font-semibold cursor-pointer' >
                Resend Verification Email
              </div>
            </>
          )}
        </div>
      </div>
    // </div>
  );
};

export default VerifyEmailPage;
