import { GoogleOAuthProvider } from '@react-oauth/google'
import React from 'react'

const layout = ({ children }: { children: React.ReactNode }) => {
    return (
        <div className="flex items-center justify-center min-h-screen bg-custom-skin bg-custom-secondary">
            <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID as string}>
                {children}
            </GoogleOAuthProvider>
        </div>
    )
}

export default layout