"use client";
import React, { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { z } from "zod";

import AppServices from "@/app/services/AppServices";
import { toast } from "sonner";
import { Skeleton } from "@mui/material";
import InputWithLabel from "@/app/components/ui/InputWithLabel";
import Button from "@/app/components/ui/Button";

// Validation schema
const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, "Password must be at least 6 characters long"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

const Page = () => {
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({
    newPassword: "",
    confirmPassword: "",
    api: "",
  });
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [passLoading, setPassLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!token) {
      toast.error("Reset token is missing");
      router.push("/forgot-password");
      return;
    }
  }, [token, router]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = { newPassword: "", confirmPassword: "", api: "" };

    // Validate password
    try {
      passwordSchema.parse(formData);
    } catch (error: any) {
      const fieldErrors = error.format();
      if (fieldErrors.newPassword?._errors) {
        newErrors.newPassword = fieldErrors.newPassword._errors[0];
        isValid = false;
      }
    }

    // Check if passwords match
    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !token) {
      return;
    }

    try {
      const payload = {
        token: token,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      };
      setPassLoading(true);
      const response = await AppServices.ResetPasswordField(payload);

      if (response.status === 200) {
        toast.success("Password reset successfully");
        localStorage.removeItem("reset-email");
        router.push("/login");
      } else {
        setErrors((prev) => ({
          ...prev,
          api: response.data?.message || "Failed to reset password",
        }));
        toast.error(response.data?.message || "Failed to reset password");
      }
    } catch (error: any) {
      console.error("Fetch error:", error);
      const errorMessage = error.message || "Something went wrong";
      setErrors((prev) => ({ ...prev, api: errorMessage }));
      toast.error(errorMessage);
    } finally {
      setPassLoading(false);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen p-4">
      {loading ? (
        <Skeleton variant="rectangular" width={400} height={400} />
      ) : (
        <div className="w-full max-w-md bg-white shadow-md p-6 rounded-[15px]">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-semibold font-conthrax mb-2">
              Reset your password
            </h1>
            <p className="text-gray-600">
              Enter a new password below to change your password
            </p>
          </div>

          {errors.api && (
            <div className="p-3 mb-4 bg-red-50 text-red-500 rounded-md">
              {errors.api}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <InputWithLabel
                label="New Password"
                id="newPassword"
                type={"password"}
                placeholder="Enter your new password"
                className="!bg-[#e8f0fe] pr-10"
                value={formData.newPassword}
                onChange={(e) =>
                  setFormData({ ...formData, newPassword: e.target.value })
                }
                error={errors.newPassword}
              />
            </div>

            <div className="relative">
              <InputWithLabel
                label="Confirm Password"
                id="confirmPassword"
                type={"password"}
                placeholder="Confirm your password"
                className="!bg-[#e8f0fe] pr-10"
                value={formData.confirmPassword}
                onChange={(e) =>
                  setFormData({ ...formData, confirmPassword: e.target.value })
                }
                error={errors.confirmPassword}
              />
            </div>

            <Button
              type="submit"
              className="w-full mt-6"
              isLoading={passLoading}
            >
              Reset Password
            </Button>
          </form>
        </div>
      )}
    </div>
  );
};

export default Page;
