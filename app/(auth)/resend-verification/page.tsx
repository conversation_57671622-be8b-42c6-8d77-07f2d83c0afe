"use client";

import Button from "@/app/components/ui/Button";
import InputWithLabel from "@/app/components/ui/InputWithLabel";
import appServiceInstance from "@/app/services/AppServices";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

const Page = () => {
  const [email, setEmail] = useState("");
  const [err, setError] = useState("");
  const [apiError, setApiError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  //handleResendEmail
  const handleResendEmail = async () => {
    if (!email) {
      setError("Email is required");
      return;
    }
    setLoading(true);
    try {
      const response = await appServiceInstance.resendVerificationEmail(email);
      if (response.status === 200) {
        toast.success(
          "Verification email sent successfully. Please check your email."
        );
      } else {
        setApiError(
          response.data?.message || "Failed to resend verification email"
        );
      }
    } catch (error:any) {
      toast.error(
        error.message || "Failed to resend verification email. Please try again later."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="min-h-screen flex items-center justify-center px-4">
        <div className="max-w-xl w-full bg-white rounded-lg shadow-lg p-8 text-center flex flex-col gap-4">
          <div className="mb-2 flex justify-center">
            <Image
              src="/img/logo.png"
              alt="Will By Will Logo"
              width={300}
              height={50}
              className="h-16 w-auto"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 font-conthrax mb-2">
            Resend Verification Email
          </h1>

          <div>
            <InputWithLabel
              label="Email"
              id="email"
              placeholder="Enter your email"
              type="email"
              className="w-full"
              onChange={(e) => {
                setError("");
                setApiError("");
                setEmail(e.target.value);
              }}
              onBlur={() => {
                if (!email) {
                  setError("Email is required");
                } else {
                  setError("");
                }
              }}
              error={err}
              required
            />
            {apiError && <p className="text-red-500 mt-1">{apiError}</p>}
          </div>

          <Button
            className="w-full"
            onClick={handleResendEmail}
            isLoading={loading}
          >
            Resend Email
          </Button>
          <div>
            <p
              className="text-black underline font-semibold -mt-4 cursor-pointer"
              onClick={() => router.push("/login")}
            >
              go to login
            </p>
          </div>
          <div>
            <p className="text-gray-600">
              If you don&apos;t receive the email, check your spam folder or try
              again later.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
